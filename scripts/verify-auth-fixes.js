#!/usr/bin/env node

/**
 * Auth Fixes Verification Script
 * 
 * This script verifies that critical authentication fixes are present
 * in the codebase and have not been accidentally reverted.
 * 
 * Usage: node scripts/verify-auth-fixes.js
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ CRITICAL: ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  WARNING: ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// File paths to check
const filesToCheck = {
  authHook: path.join(__dirname, '../Frontend/src/hooks/useAuth.tsx'),
  authService: path.join(__dirname, '../Frontend/src/services/authService.ts'),
  authRedirect: path.join(__dirname, '../Frontend/src/hooks/useAuthRedirect.ts')
};

// Verification checks
const checks = [
  {
    name: 'Role Normalization Fix',
    file: 'authHook',
    pattern: /const normalizedRole = role\.toUpperCase\(\)/,
    description: 'Ensures role-based access works regardless of case sensitivity',
    critical: true
  },
  {
    name: 'Token Interface Definition',
    file: 'authHook',
    pattern: /token:\s*string\s*\|\s*null/,
    description: 'Token must be defined in AuthContextType interface',
    critical: true
  },
  {
    name: 'Token State Management',
    file: 'authHook',
    pattern: /setToken\(currentToken\)/,
    description: 'Token state must be managed in loadUserFromToken function',
    critical: true
  },
  {
    name: 'Token in Context Value',
    file: 'authHook',
    pattern: /token,/,
    description: 'Token must be included in context value object',
    critical: true
  },
  {
    name: 'Token State Variable',
    file: 'authHook',
    pattern: /const \[token, setToken\] = useState<string \| null>\(null\)/,
    description: 'Token state variable must be declared',
    critical: true
  },
  {
    name: 'Token Cleanup in SignOut',
    file: 'authHook',
    pattern: /setToken\(null\)/,
    description: 'Token must be cleared on sign out',
    critical: true
  },
  {
    name: 'Admin Status Check Logic',
    file: 'authHook',
    pattern: /normalizedRole === 'ADMIN' \|\| normalizedRole === 'SUPER_ADMIN'/,
    description: 'Admin status check must use normalized role',
    critical: true
  }
];

function readFileContent(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    logError(`Cannot read file: ${filePath}`);
    logError(`Error: ${error.message}`);
    return null;
  }
}

function runCheck(check, content) {
  const found = check.pattern.test(content);
  
  if (found) {
    logSuccess(`${check.name}: Present`);
    return true;
  } else {
    if (check.critical) {
      logError(`${check.name}: Missing`);
      logError(`  Description: ${check.description}`);
    } else {
      logWarning(`${check.name}: Missing`);
      logWarning(`  Description: ${check.description}`);
    }
    return false;
  }
}

function verifyAuthFixes() {
  log('\n' + '='.repeat(60), 'bold');
  log('🔍 VERIFYING CRITICAL AUTH FIXES', 'bold');
  log('='.repeat(60), 'bold');
  
  let allCriticalPassed = true;
  let totalChecks = 0;
  let passedChecks = 0;
  
  // Read file contents
  const fileContents = {};
  for (const [key, filePath] of Object.entries(filesToCheck)) {
    logInfo(`Reading ${filePath}...`);
    const content = readFileContent(filePath);
    if (content === null) {
      logError(`Failed to read ${key} file. Aborting verification.`);
      process.exit(1);
    }
    fileContents[key] = content;
  }
  
  log('\n' + '-'.repeat(60), 'blue');
  log('Running verification checks...', 'blue');
  log('-'.repeat(60), 'blue');
  
  // Run all checks
  for (const check of checks) {
    totalChecks++;
    const content = fileContents[check.file];
    const passed = runCheck(check, content);
    
    if (passed) {
      passedChecks++;
    } else if (check.critical) {
      allCriticalPassed = false;
    }
  }
  
  // Summary
  log('\n' + '='.repeat(60), 'bold');
  log('📊 VERIFICATION SUMMARY', 'bold');
  log('='.repeat(60), 'bold');
  
  log(`Total checks: ${totalChecks}`);
  log(`Passed: ${passedChecks}`, passedChecks === totalChecks ? 'green' : 'yellow');
  log(`Failed: ${totalChecks - passedChecks}`, totalChecks - passedChecks === 0 ? 'green' : 'red');
  
  if (allCriticalPassed && passedChecks === totalChecks) {
    log('\n🎉 ALL CRITICAL AUTH FIXES ARE PRESENT!', 'green');
    log('✅ Safe to proceed with commit/deployment', 'green');
    process.exit(0);
  } else if (allCriticalPassed) {
    log('\n✅ All critical fixes are present', 'green');
    log('⚠️  Some non-critical checks failed', 'yellow');
    process.exit(0);
  } else {
    log('\n🚨 CRITICAL AUTH FIXES ARE MISSING!', 'red');
    log('❌ DO NOT COMMIT OR DEPLOY', 'red');
    log('\nPlease restore the missing fixes before proceeding.', 'red');
    log('Refer to docs/AUTH_FIXES_CHECKLIST.md for details.', 'blue');
    process.exit(1);
  }
}

// Additional helper functions
function showHelp() {
  log('\nAuth Fixes Verification Script', 'bold');
  log('Usage: node scripts/verify-auth-fixes.js [options]', 'blue');
  log('\nOptions:');
  log('  --help, -h    Show this help message');
  log('  --verbose, -v Show detailed output');
  log('\nThis script verifies that critical authentication fixes');
  log('have not been accidentally reverted.');
}

// Parse command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  showHelp();
  process.exit(0);
}

// Run the verification
try {
  verifyAuthFixes();
} catch (error) {
  logError(`Verification script failed: ${error.message}`);
  logError('Stack trace:');
  console.error(error.stack);
  process.exit(1);
}
