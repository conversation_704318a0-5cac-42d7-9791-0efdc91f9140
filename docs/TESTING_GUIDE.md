# Testing Guide

## Overview

This guide outlines the comprehensive testing strategy for the admin features, following <PERSON>'s established patterns for reliable and maintainable tests.

## Testing Philosophy

### Core Principles

1. **Test Behavior, Not Implementation** - Focus on what the component does, not how it does it
2. **User-Centric Testing** - Test from the user's perspective using accessible queries
3. **Comprehensive Coverage** - Cover happy paths, edge cases, and error scenarios
4. **Performance Testing** - Ensure components handle large datasets efficiently
5. **Accessibility Testing** - Verify components work with assistive technologies

### Testing Pyramid

```
    E2E Tests (Few)
       ↑
  Integration Tests (Some)
       ↑
   Unit Tests (Many)
```

## Test Structure

### File Organization

```
src/
├── components/
│   ├── admin/
│   │   ├── __tests__/
│   │   │   ├── AdminAgentManagement.test.tsx
│   │   │   ├── AdminApplicationsList.test.tsx
│   │   │   └── ApplicationAnalytics.test.tsx
│   │   └── ...
├── hooks/
│   ├── __tests__/
│   │   ├── useErrorHandler.test.ts
│   │   └── useOptimizedFetch.test.ts
├── services/
│   ├── __tests__/
│   │   └── apiService.test.ts
├── utils/
│   ├── __tests__/
│   │   ├── errorHandler.test.ts
│   │   └── performanceOptimizer.test.ts
└── test/
    ├── setup.ts
    └── utils.ts
```

### Naming Conventions

- Test files: `ComponentName.test.tsx` or `utilityName.test.ts`
- Test suites: Descriptive names matching the component/utility
- Test cases: Use `it('should do something when condition')` format

## Testing Tools

### Core Libraries

- **Vitest** - Fast unit test runner with great TypeScript support
- **React Testing Library** - User-centric component testing
- **Jest DOM** - Custom matchers for DOM assertions
- **User Event** - Realistic user interaction simulation

### Configuration

```typescript
// vitest.config.ts
export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
});
```

## Test Categories

### 1. Component Tests

#### Structure
```typescript
describe('ComponentName', () => {
  describe('Rendering', () => {
    // Basic rendering tests
  });

  describe('User Interactions', () => {
    // Click, type, select interactions
  });

  describe('Data Loading', () => {
    // Loading states, data display
  });

  describe('Error Handling', () => {
    // Error states, fallbacks
  });

  describe('Accessibility', () => {
    // ARIA labels, keyboard navigation
  });

  describe('Performance', () => {
    // Large datasets, debouncing
  });
});
```

#### Example Test
```typescript
it('filters agents by search term', async () => {
  const user = userEvent.setup();
  render(<AdminAgentManagement />, { wrapper: createWrapper() });

  await waitFor(() => {
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  const searchInput = screen.getByPlaceholderText('Search agents...');
  await user.type(searchInput, 'John');

  await waitFor(() => {
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
  });
});
```

### 2. Hook Tests

#### Structure
```typescript
describe('useHookName', () => {
  describe('Initial State', () => {
    // Default values, loading states
  });

  describe('Data Fetching', () => {
    // API calls, caching
  });

  describe('Error Handling', () => {
    // Error states, retries
  });

  describe('State Updates', () => {
    // State changes, side effects
  });
});
```

#### Example Test
```typescript
it('caches data and avoids duplicate requests', async () => {
  const mockFetch = vi.fn().mockResolvedValue('test-data');
  
  const { result, rerender } = renderHook(() =>
    useOptimizedFetch(mockFetch, 'test-key')
  );

  await waitFor(() => {
    expect(result.current.data).toBe('test-data');
  });

  rerender();
  
  expect(mockFetch).toHaveBeenCalledTimes(1);
});
```

### 3. Service Tests

#### Structure
```typescript
describe('ServiceName', () => {
  describe('Authentication', () => {
    // Token handling, headers
  });

  describe('API Methods', () => {
    // Request/response handling
  });

  describe('Error Handling', () => {
    // HTTP errors, network failures
  });

  describe('Data Transformation', () => {
    // Request/response formatting
  });
});
```

### 4. Utility Tests

#### Structure
```typescript
describe('UtilityName', () => {
  describe('Core Functionality', () => {
    // Main features
  });

  describe('Edge Cases', () => {
    // Boundary conditions
  });

  describe('Error Scenarios', () => {
    // Invalid inputs, failures
  });

  describe('Performance', () => {
    // Large inputs, timing
  });
});
```

## Best Practices

### 1. Query Priorities

Use queries in this order of preference:

1. **Accessible to everyone**: `getByRole`, `getByLabelText`, `getByPlaceholderText`, `getByText`
2. **Semantic queries**: `getByAltText`, `getByTitle`
3. **Test IDs**: `getByTestId` (last resort)

### 2. Async Testing

```typescript
// ✅ Good - Wait for specific condition
await waitFor(() => {
  expect(screen.getByText('Data loaded')).toBeInTheDocument();
});

// ❌ Bad - Arbitrary timeout
await new Promise(resolve => setTimeout(resolve, 1000));
```

### 3. User Interactions

```typescript
// ✅ Good - Realistic user interaction
const user = userEvent.setup();
await user.click(button);
await user.type(input, 'text');

// ❌ Bad - Direct event firing
fireEvent.click(button);
fireEvent.change(input, { target: { value: 'text' } });
```

### 4. Mocking

```typescript
// ✅ Good - Mock at the boundary
vi.mock('@/services/apiService');

// ✅ Good - Restore after each test
afterEach(() => {
  vi.clearAllMocks();
});

// ❌ Bad - Mock internal implementation details
vi.mock('./InternalComponent');
```

### 5. Test Data

```typescript
// ✅ Good - Use factory functions
const mockAgent = createMockAgent({
  full_name: 'Test Agent',
  status: 'verified'
});

// ❌ Bad - Inline objects
const mockAgent = {
  id: '1',
  full_name: 'Test Agent',
  // ... many properties
};
```

## Running Tests

### Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test AdminAgentManagement

# Run tests matching pattern
npm test -- --grep "error handling"
```

### Coverage Reports

Coverage reports are generated in the `coverage/` directory:

- **HTML Report**: `coverage/index.html` - Interactive coverage browser
- **JSON Report**: `coverage/coverage-final.json` - Machine-readable data
- **Text Report**: Console output during test runs

### CI/CD Integration

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:coverage
      - uses: codecov/codecov-action@v3
```

## Debugging Tests

### Common Issues

1. **Async Operations**: Use `waitFor` for async state changes
2. **Cleanup**: Ensure proper cleanup between tests
3. **Mocking**: Verify mocks are properly configured
4. **Timing**: Use fake timers for time-dependent code

### Debug Tools

```typescript
// Debug rendered component
screen.debug();

// Debug specific element
screen.debug(screen.getByRole('button'));

// Log queries
screen.logTestingPlaygroundURL();
```

## Performance Testing

### Large Dataset Testing

```typescript
it('handles large datasets efficiently', async () => {
  const largeDataset = Array.from({ length: 1000 }, createMockAgent);
  
  const startTime = performance.now();
  render(<AdminAgentManagement data={largeDataset} />);
  const endTime = performance.now();
  
  expect(endTime - startTime).toBeLessThan(1000); // 1 second
});
```

### Memory Leak Testing

```typescript
it('cleans up properly on unmount', () => {
  const { unmount } = render(<Component />);
  
  // Verify cleanup
  unmount();
  
  // Check for memory leaks
  expect(/* cleanup verification */).toBeTruthy();
});
```

## Accessibility Testing

### ARIA Testing

```typescript
it('has proper ARIA labels', () => {
  render(<AdminAgentManagement />);
  
  expect(screen.getByRole('heading', { name: 'Agent Management' }))
    .toBeInTheDocument();
  expect(screen.getByRole('textbox', { name: /search/i }))
    .toBeInTheDocument();
});
```

### Keyboard Navigation

```typescript
it('supports keyboard navigation', async () => {
  const user = userEvent.setup();
  render(<AdminAgentManagement />);
  
  await user.tab();
  expect(screen.getByRole('textbox')).toHaveFocus();
  
  await user.tab();
  expect(screen.getByRole('button')).toHaveFocus();
});
```

## Continuous Improvement

### Metrics to Track

- **Coverage Percentage**: Aim for 80%+ across all metrics
- **Test Execution Time**: Keep under 30 seconds for full suite
- **Flaky Test Rate**: Less than 1% of tests should be flaky
- **Bug Escape Rate**: Track bugs found in production vs. tests

### Regular Reviews

- **Weekly**: Review failed tests and flaky tests
- **Monthly**: Analyze coverage reports and identify gaps
- **Quarterly**: Review testing strategy and update guidelines

---

*This testing guide ensures comprehensive coverage of admin features while maintaining Julius's high standards for code quality and reliability.*
