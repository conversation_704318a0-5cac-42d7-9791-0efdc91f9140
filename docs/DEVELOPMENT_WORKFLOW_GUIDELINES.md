# Development Workflow Guidelines

## Purpose
This document establishes guidelines to prevent important bug fixes from being accidentally reverted in future commits and ensures code quality consistency.

## Core Principles

### 1. Code Review Process
- **All commits must be reviewed** before merging to main branch
- **Critical bug fixes** should be clearly marked in commit messages with `fix:` prefix
- **Breaking changes** should be marked with `BREAKING CHANGE:` in commit body
- **Role-based access fixes** should be thoroughly tested before merging

### 2. Commit Message Standards
```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `fix:` - Bug fixes (critical for tracking)
- `feat:` - New features
- `refactor:` - Code refactoring
- `test:` - Adding tests
- `docs:` - Documentation changes

**Examples:**
```
fix(auth): normalize user roles in checkAdminStatus to prevent access failures

This fix ensures role-based access works correctly regardless of case
sensitivity from the backend API.

Fixes: #123
```

### 3. Testing Requirements

#### Before Committing
- [ ] Run unit tests: `npm test`
- [ ] Run integration tests for auth flows
- [ ] Test role-based access with different user types
- [ ] Verify token persistence on page refresh

#### Critical Areas to Test
1. **Authentication Flow**
   - Login/logout functionality
   - Token refresh mechanism
   - Role-based redirects

2. **Role-Based Access**
   - Admin access to admin routes
   - Agent access to agent dashboard
   - Landlord access to landlord portal
   - Tenant access to tenant portal

3. **Token Management**
   - Token availability after page refresh
   - Token expiration handling
   - Automatic token refresh

### 4. Pre-Merge Checklist

#### For Bug Fixes
- [ ] Identify root cause of the bug
- [ ] Write test case that reproduces the bug
- [ ] Implement fix
- [ ] Verify test passes
- [ ] Test manually in browser
- [ ] Document the fix in commit message
- [ ] Add regression test if applicable

#### For Features
- [ ] Feature works as specified
- [ ] No existing functionality broken
- [ ] Tests added for new functionality
- [ ] Documentation updated

### 5. Branch Protection Rules

#### Main Branch
- Require pull request reviews before merging
- Require status checks to pass before merging
- Require branches to be up to date before merging
- Restrict pushes to main branch

#### Development Workflow
1. Create feature/fix branch from main
2. Make changes and commit with proper messages
3. Push branch and create pull request
4. Request review from team member
5. Address review feedback
6. Merge only after approval

### 6. Critical Code Areas

#### Authentication (Frontend/src/hooks/useAuth.tsx)
**Key Functions to Protect:**
- `checkAdminStatus()` - Role normalization logic
- `loadUserFromToken()` - Token state management
- `refreshUser()` - Token refresh logic

**Common Issues to Watch:**
- Role case sensitivity
- Token state not persisting
- Missing error handling

#### Role-Based Access
**Files to Monitor:**
- `Frontend/src/hooks/useAuthRedirect.ts`
- `Frontend/src/hooks/useRoleAccess.ts`
- `Backend/src/modules/auth/guards/roles.guard.ts`

### 7. Automated Checks

#### Pre-commit Hooks (Recommended)
```bash
# Install husky for git hooks
npm install --save-dev husky

# Add pre-commit hook
npx husky add .husky/pre-commit "npm test"
npx husky add .husky/pre-commit "npm run lint"
```

#### CI/CD Pipeline
- Run all tests on pull requests
- Block merge if tests fail
- Run security scans
- Check code coverage

### 8. Documentation Requirements

#### For Bug Fixes
- Document the issue in commit message
- Add comments explaining complex logic
- Update relevant documentation

#### For New Features
- Update API documentation
- Add usage examples
- Update user guides

### 9. Rollback Procedures

#### If a Reversion is Detected
1. **Immediate Action:**
   - Create hotfix branch
   - Restore the reverted functionality
   - Add regression tests
   - Deploy fix immediately

2. **Root Cause Analysis:**
   - Identify how the reversion occurred
   - Review the merge process
   - Update workflow to prevent recurrence

3. **Communication:**
   - Notify team of the issue
   - Document lessons learned
   - Update guidelines if needed

### 10. Team Responsibilities

#### Developers
- Follow commit message standards
- Write comprehensive tests
- Review code thoroughly
- Test manually before committing

#### Reviewers
- Check for potential regressions
- Verify tests cover edge cases
- Ensure documentation is updated
- Test critical paths manually

#### Team Lead
- Enforce workflow guidelines
- Monitor for recurring issues
- Update guidelines as needed
- Ensure team training

## Implementation Timeline

### Phase 1 (Immediate)
- [ ] Implement branch protection rules
- [ ] Set up pre-commit hooks
- [ ] Document critical code areas

### Phase 2 (Within 1 week)
- [ ] Add comprehensive tests for auth flows
- [ ] Set up automated CI/CD checks
- [ ] Train team on new workflow

### Phase 3 (Ongoing)
- [ ] Regular workflow reviews
- [ ] Continuous improvement
- [ ] Monitor and adjust guidelines

## Contact
For questions about this workflow, contact the development team lead.

---
*Last updated: July 25, 2025*
*Version: 1.0*
