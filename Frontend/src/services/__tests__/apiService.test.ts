/**
 * Test Suite for API Service
 * 
 * Integration tests for API service methods including authentication,
 * error handling, and data transformation following <PERSON>'s patterns.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ApiService } from '../apiService';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('ApiService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('mock-token');
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Authentication', () => {
    it('includes auth token in requests', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: 'test' }),
      });

      await ApiService.getStats();

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/stats'),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'Bearer mock-token',
          }),
        })
      );
    });

    it('works without auth token', async () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: 'test' }),
      });

      await ApiService.getStats();

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/stats'),
        expect.objectContaining({
          headers: expect.not.objectContaining({
            Authorization: expect.anything(),
          }),
        })
      );
    });
  });

  describe('Error Handling', () => {
    it('throws error for non-ok responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
      });

      await expect(ApiService.getStats()).rejects.toThrow('HTTP error! status: 404');
    });

    it('handles network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(ApiService.getStats()).rejects.toThrow('Network error');
    });

    it('handles JSON parsing errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.reject(new Error('Invalid JSON')),
      });

      await expect(ApiService.getStats()).rejects.toThrow('Invalid JSON');
    });
  });

  describe('Statistics API', () => {
    it('fetches stats successfully', async () => {
      const mockStats = {
        totalApplications: 100,
        pendingApplications: 25,
        approvedApplications: 60,
        rejectedApplications: 15,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockStats),
      });

      const result = await ApiService.getStats();

      expect(result).toEqual(mockStats);
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/stats'),
        expect.objectContaining({
          method: 'GET',
        })
      );
    });
  });

  describe('Applications API', () => {
    it('fetches applications with filters', async () => {
      const mockApplications = {
        data: [
          { id: '1', full_name: 'John Doe', status: 'pending' },
          { id: '2', full_name: 'Jane Smith', status: 'approved' },
        ],
        total: 2,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockApplications),
      });

      const filters = {
        page: 1,
        limit: 10,
        status: 'pending',
        search: 'John',
      };

      const result = await ApiService.getApplications(filters);

      expect(result).toEqual(mockApplications);
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/applications?page=1&limit=10&status=pending&search=John'),
        expect.objectContaining({
          method: 'GET',
        })
      );
    });

    it('updates application status', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({}),
      });

      await ApiService.updateApplicationStatus('app-123', 'approved', 'Looks good');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/applications/app-123/status'),
        expect.objectContaining({
          method: 'PATCH',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify({
            status: 'approved',
            reviewer_notes: 'Looks good',
          }),
        })
      );
    });

    it('performs bulk update on applications', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({}),
      });

      const applicationIds = ['app-1', 'app-2', 'app-3'];
      const updates = { status: 'approved' };

      await ApiService.bulkUpdateApplications(applicationIds, updates);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/applications/bulk-update'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            application_ids: applicationIds,
            updates,
          }),
        })
      );
    });
  });

  describe('Agents API', () => {
    it('fetches agents with pagination', async () => {
      const mockAgents = {
        data: [
          { id: '1', full_name: 'Agent One', status: 'verified' },
          { id: '2', full_name: 'Agent Two', status: 'pending' },
        ],
        total: 2,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockAgents),
      });

      const filters = {
        page: 1,
        limit: 20,
        status: 'verified',
        location: 'Lagos',
      };

      const result = await ApiService.getAgents(filters);

      expect(result).toEqual(mockAgents);
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/agents?page=1&limit=20&status=verified&location=Lagos'),
        expect.objectContaining({
          method: 'GET',
        })
      );
    });

    it('fetches agent statistics', async () => {
      const mockStats = {
        total: 50,
        verified: 35,
        pending: 10,
        suspended: 5,
        active_this_month: 40,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockStats),
      });

      const result = await ApiService.getAgentStats();

      expect(result).toEqual(mockStats);
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/agents/stats'),
        expect.objectContaining({
          method: 'GET',
        })
      );
    });

    it('updates agent status', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({}),
      });

      await ApiService.updateAgentStatus('agent-123', 'verified');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/agents/agent-123/status'),
        expect.objectContaining({
          method: 'PATCH',
          body: JSON.stringify({ status: 'verified' }),
        })
      );
    });

    it('performs bulk update on agents', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({}),
      });

      const agentIds = ['agent-1', 'agent-2'];
      const updates = { action: 'verify' };

      await ApiService.bulkUpdateAgents(agentIds, updates);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/agents/bulk-update'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            agent_ids: agentIds,
            updates,
          }),
        })
      );
    });
  });

  describe('Analytics API', () => {
    it('fetches application trends', async () => {
      const mockTrends = {
        monthlyData: [
          { month: 'Jan', applications: 45, approved: 38, rejected: 5 },
          { month: 'Feb', applications: 52, approved: 44, rejected: 6 },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockTrends),
      });

      const result = await ApiService.getApplicationTrends();

      expect(result).toEqual(mockTrends);
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/analytics/applications/trends'),
        expect.objectContaining({
          method: 'GET',
        })
      );
    });

    it('fetches average processing time', async () => {
      const mockProcessingTime = {
        averageDays: 3.5,
        byStatus: {
          approved: 2.8,
          rejected: 1.2,
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockProcessingTime),
      });

      const result = await ApiService.getAverageProcessingTime();

      expect(result).toEqual(mockProcessingTime);
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/analytics/applications/processing-time'),
        expect.objectContaining({
          method: 'GET',
        })
      );
    });

    it('fetches admin performance with filters', async () => {
      const mockPerformance = {
        metrics: {
          applications_reviewed: 45,
          average_review_time_hours: 2.5,
          approval_rate: 85.5,
        },
        dailyActivity: [
          { date: '2024-01-01', applications: 5, approvals: 4, avgTime: 2.1 },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockPerformance),
      });

      const result = await ApiService.getAdminPerformance('admin-123', '7days');

      expect(result).toEqual(mockPerformance);
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/analytics/admin/performance?admin_id=admin-123&date_range=7days'),
        expect.objectContaining({
          method: 'GET',
        })
      );
    });
  });

  describe('File Upload', () => {
    it('uploads file successfully', async () => {
      const mockResponse = {
        data: { url: 'https://example.com/uploaded-file.jpg' },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
      const result = await ApiService.uploadFile(mockFile, 'documents');

      expect(result).toBe('https://example.com/uploaded-file.jpg');
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/upload'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            Authorization: 'Bearer mock-token',
          }),
          body: expect.any(FormData),
        })
      );
    });

    it('handles upload failure', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 413,
        statusText: 'Payload Too Large',
      });

      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });

      await expect(ApiService.uploadFile(mockFile)).rejects.toThrow('File upload failed');
    });
  });

  describe('Request Configuration', () => {
    it('sets correct content type for JSON requests', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({}),
      });

      await ApiService.updateApplicationStatus('app-123', 'approved');

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        })
      );
    });

    it('omits content type for FormData requests', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: { url: 'test.jpg' } }),
      });

      const mockFile = new File(['test'], 'test.jpg');
      await ApiService.uploadFile(mockFile);

      const fetchCall = mockFetch.mock.calls[0];
      const headers = fetchCall[1].headers;
      
      expect(headers['Content-Type']).toBeUndefined();
    });

    it('handles empty query parameters', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: [], total: 0 }),
      });

      await ApiService.getAgents({});

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/agents'),
        expect.any(Object)
      );

      const url = mockFetch.mock.calls[0][0];
      expect(url).not.toContain('?');
    });

    it('properly encodes query parameters', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: [], total: 0 }),
      });

      await ApiService.getAgents({
        search: 'John Doe & Associates',
        location: 'Victoria Island',
      });

      const url = mockFetch.mock.calls[0][0];
      expect(url).toContain('search=John%20Doe%20%26%20Associates');
      expect(url).toContain('location=Victoria%20Island');
    });
  });
});
