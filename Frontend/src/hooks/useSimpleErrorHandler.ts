/**
 * Simple Error Handler Hook
 * 
 * Basic error handling without complex dependencies
 */

import { useState, useCallback } from 'react';

export interface SimpleErrorHandlerReturn {
  isLoading: boolean;
  error: string | null;
  executeApiCall: <T>(
    apiCall: () => Promise<T>,
    context?: string,
    options?: { retryable?: boolean; maxRetries?: number }
  ) => Promise<T | null>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useSimpleErrorHandler = (context: string = 'Component'): SimpleErrorHandlerReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setIsLoading(loading);
  }, []);

  const executeApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    callContext: string = 'API Call',
    options: { retryable?: boolean; maxRetries?: number } = {}
  ): Promise<T | null> => {
    const { retryable = false, maxRetries = 3 } = options;
    
    setIsLoading(true);
    setError(null);

    let lastError: Error | null = null;
    const attempts = retryable ? maxRetries : 1;

    for (let attempt = 1; attempt <= attempts; attempt++) {
      try {
        const result = await apiCall();
        setIsLoading(false);
        return result;
      } catch (err) {
        lastError = err as Error;
        
        console.error(`${context} - ${callContext} failed (attempt ${attempt}/${attempts}):`, err);
        
        if (attempt === attempts) {
          // Final attempt failed
          const errorMessage = lastError?.message || 'An unexpected error occurred';
          setError(errorMessage);
          setIsLoading(false);
          
          // Don't throw, just return null and let component handle the error state
          return null;
        }
        
        // Wait before retry
        if (retryable && attempt < attempts) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    return null;
  }, [context]);

  return {
    isLoading,
    error,
    executeApiCall,
    clearError,
    setLoading,
  };
};

// Alias for backward compatibility
export const useApiErrorHandler = useSimpleErrorHandler;
