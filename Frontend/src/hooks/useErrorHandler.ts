/**
 * React Hook for Error Handling
 * 
 * Provides a convenient way to handle errors in React components
 * following <PERSON>'s established patterns.
 */

import { useCallback, useState } from 'react';
import { 
  errorHandler, 
  ErrorType, 
  ErrorSeverity, 
  ErrorContext, 
  AppError,
  handleApiError,
  handleNetworkError,
  handleValidationError,
  createRetryFunction
} from '@/utils/errorHandler';

interface UseErrorHandlerReturn {
  // Error state
  error: AppError | null;
  isError: boolean;
  clearError: () => void;
  
  // Error handling functions
  handleError: (error: AppError) => void;
  handleApiError: (error: any, context: ErrorContext, customMessage?: string) => AppError;
  handleNetworkError: (error: Error, context: ErrorContext, customMessage?: string) => AppError;
  handleValidationError: (message: string, context: ErrorContext, fieldErrors?: Record<string, string>) => AppError;
  
  // Async operation wrapper with error handling
  withErrorHandling: <T>(
    operation: () => Promise<T>,
    context: ErrorContext,
    options?: {
      customMessage?: string;
      showLoading?: boolean;
      retryable?: boolean;
      maxRetries?: number;
    }
  ) => Promise<T | null>;
  
  // Loading state for async operations
  isLoading: boolean;
}

/**
 * Hook for handling errors in React components
 */
export const useErrorHandler = (componentName?: string): UseErrorHandlerReturn => {
  const [error, setError] = useState<AppError | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const handleErrorCallback = useCallback((appError: AppError) => {
    setError(appError);
    errorHandler.handleError(appError);
  }, []);

  const handleApiErrorCallback = useCallback((
    apiError: any,
    context: ErrorContext,
    customMessage?: string
  ) => {
    const appError = handleApiError(apiError, {
      ...context,
      component: componentName || context.component,
    }, customMessage);
    setError(appError);
    return appError;
  }, [componentName]);

  const handleNetworkErrorCallback = useCallback((
    networkError: Error,
    context: ErrorContext,
    customMessage?: string
  ) => {
    const appError = handleNetworkError(networkError, {
      ...context,
      component: componentName || context.component,
    }, customMessage);
    setError(appError);
    return appError;
  }, [componentName]);

  const handleValidationErrorCallback = useCallback((
    message: string,
    context: ErrorContext,
    fieldErrors?: Record<string, string>
  ) => {
    const appError = handleValidationError(message, {
      ...context,
      component: componentName || context.component,
    }, fieldErrors);
    setError(appError);
    return appError;
  }, [componentName]);

  const withErrorHandling = useCallback(async <T>(
    operation: () => Promise<T>,
    context: ErrorContext,
    options: {
      customMessage?: string;
      showLoading?: boolean;
      retryable?: boolean;
      maxRetries?: number;
    } = {}
  ): Promise<T | null> => {
    const {
      customMessage,
      showLoading = true,
      retryable = false,
      maxRetries = 3
    } = options;

    if (showLoading) {
      setIsLoading(true);
    }

    try {
      clearError();

      let result: T;

      if (retryable) {
        const retryFunction = createRetryFunction(
          operation,
          {
            ...context,
            component: componentName || context.component,
          },
          maxRetries
        );
        result = await retryFunction();
      } else {
        result = await operation();
      }

      return result;
    } catch (err: any) {
      // Determine error type and handle accordingly
      if (err.name === 'AbortError') {
        handleNetworkErrorCallback(err, context, 'Operation was cancelled');
      } else if (err.response) {
        handleApiErrorCallback(err, context, customMessage);
      } else if (err.request) {
        handleNetworkErrorCallback(err, context, customMessage);
      } else if (err instanceof AppError) {
        handleErrorCallback(err);
      } else {
        handleApiErrorCallback(err, context, customMessage);
      }

      return null;
    } finally {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  }, [
    componentName,
    clearError,
    handleErrorCallback,
    handleApiErrorCallback,
    handleNetworkErrorCallback
  ]);

  return {
    error,
    isError: error !== null,
    clearError,
    handleError: handleErrorCallback,
    handleApiError: handleApiErrorCallback,
    handleNetworkError: handleNetworkErrorCallback,
    handleValidationError: handleValidationErrorCallback,
    withErrorHandling,
    isLoading,
  };
};

/**
 * Hook specifically for API operations
 */
export const useApiErrorHandler = (componentName?: string) => {
  const errorHandler = useErrorHandler(componentName);

  const executeApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    action: string,
    options?: {
      customMessage?: string;
      retryable?: boolean;
      maxRetries?: number;
    }
  ): Promise<T | null> => {
    return errorHandler.withErrorHandling(
      apiCall,
      { component: componentName, action },
      options
    );
  }, [errorHandler, componentName]);

  return {
    ...errorHandler,
    executeApiCall,
  };
};

/**
 * Hook for form validation errors
 */
export const useFormErrorHandler = (componentName?: string) => {
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const errorHandler = useErrorHandler(componentName);

  const setFieldError = useCallback((field: string, message: string) => {
    setFieldErrors(prev => ({ ...prev, [field]: message }));
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllFieldErrors = useCallback(() => {
    setFieldErrors({});
  }, []);

  const handleFormValidationError = useCallback((
    message: string,
    context: ErrorContext,
    errors?: Record<string, string>
  ) => {
    if (errors) {
      setFieldErrors(errors);
    }
    return errorHandler.handleValidationError(message, context, errors);
  }, [errorHandler]);

  const validateAndExecute = useCallback(async <T>(
    operation: () => Promise<T>,
    context: ErrorContext,
    validationRules?: Record<string, (value: any) => string | null>
  ): Promise<T | null> => {
    clearAllFieldErrors();

    // Run validation rules if provided
    if (validationRules) {
      const errors: Record<string, string> = {};
      let hasErrors = false;

      Object.entries(validationRules).forEach(([field, validator]) => {
        const error = validator((context.additionalData as any)?.[field]);
        if (error) {
          errors[field] = error;
          hasErrors = true;
        }
      });

      if (hasErrors) {
        handleFormValidationError('Please fix the validation errors', context, errors);
        return null;
      }
    }

    return errorHandler.withErrorHandling(operation, context);
  }, [errorHandler, clearAllFieldErrors, handleFormValidationError]);

  return {
    ...errorHandler,
    fieldErrors,
    setFieldError,
    clearFieldError,
    clearAllFieldErrors,
    handleFormValidationError,
    validateAndExecute,
  };
};

/**
 * Common validation rules
 */
export const validationRules = {
  required: (value: any) => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return 'This field is required';
    }
    return null;
  },
  
  email: (value: string) => {
    if (!value) return null;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) ? null : 'Please enter a valid email address';
  },
  
  phone: (value: string) => {
    if (!value) return null;
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    return phoneRegex.test(value) ? null : 'Please enter a valid phone number';
  },
  
  minLength: (min: number) => (value: string) => {
    if (!value) return null;
    return value.length >= min ? null : `Must be at least ${min} characters`;
  },
  
  maxLength: (max: number) => (value: string) => {
    if (!value) return null;
    return value.length <= max ? null : `Must be no more than ${max} characters`;
  },
  
  numeric: (value: string) => {
    if (!value) return null;
    return !isNaN(Number(value)) ? null : 'Must be a valid number';
  },
  
  positiveNumber: (value: string) => {
    if (!value) return null;
    const num = Number(value);
    return !isNaN(num) && num > 0 ? null : 'Must be a positive number';
  },
};
