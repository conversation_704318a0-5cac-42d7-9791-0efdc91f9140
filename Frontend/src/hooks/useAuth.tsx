import { useState, useEffect, createContext, useContext } from 'react';
import {
  authService,
  type AuthResponse,
  type LoginDto,
  type RegisterDto,
} from '@/services/authService';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  isEmailVerified: boolean;
  isActive: boolean;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAdmin: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: any }>;
  signUp: (
    email: string,
    password: string,
    fullName: string,
    phone: string,
    role: 'TENANT' | 'LANDLORD' | 'AGENT'
  ) => Promise<{ error?: any }>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);

  const checkAdminStatus = (role: string) => {
    setIsAdmin(role === 'ADMIN' || role === 'SUPER_ADMIN');
  };

  const loadUserFromToken = async () => {
    try {
      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        // Get full user profile from API
        const profile = await authService.getProfile();
        setUser(profile);
        checkAdminStatus(profile.role);
      } else {
        setUser(null);
        setIsAdmin(false);
      }
    } catch (error) {
      console.error('Failed to load user profile:', error);
      setUser(null);
      setIsAdmin(false);
    }
  };

  const refreshUser = async () => {
    try {
      await authService.ensureValidToken();
      await loadUserFromToken();
    } catch (error) {
      console.error('Failed to refresh user:', error);
      setUser(null);
      setIsAdmin(false);
    }
  };

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check if we have valid tokens
        const isValid = await authService.ensureValidToken();
        if (isValid) {
          await loadUserFromToken();
        } else {
          setUser(null);
          setIsAdmin(false);
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
        setUser(null);
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const loginDto: LoginDto = { email, password };
      const response: AuthResponse = await authService.login(loginDto);

      setUser(response.user);
      checkAdminStatus(response.user.role);

      return { error: null };
    } catch (error: any) {
      console.error('Sign in failed:', error);
      return { error };
    }
  };

  const signUp = async (
    email: string,
    password: string,
    fullName: string,
    phone: string,
    role: 'TENANT' | 'LANDLORD' | 'AGENT'
  ) => {
    try {
      // Split full name into first and last name
      const nameParts = fullName.trim().split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      const registerDto = {
        email,
        password,
        firstName,
        lastName,
        phone,
        role: role.toLowerCase() as 'tenant' | 'landlord' | 'agent' | 'admin',
      };

      const response: AuthResponse = await authService.register(registerDto);
      console.log('Response object:', response);

      setUser(response.user);
      checkAdminStatus(response.user.role);

      return { error: null };
    } catch (error: any) {
      console.error('Sign up failed:', error);
      return { error };
    }
  };

  const signOut = async () => {
    try {
      await authService.logout();
      setUser(null);
      setIsAdmin(false);
    } catch (error) {
      console.error('Sign out failed:', error);
      // Clear state even if API call fails
      setUser(null);
      setIsAdmin(false);
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    try {
      const response = await fetch('/api/v1/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify({ currentPassword, newPassword }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to change password');
      }
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (token: string, newPassword: string): Promise<void> => {
    try {
      const response = await fetch('/api/v1/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, newPassword }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to reset password');
      }
    } catch (error) {
      throw error;
    }
  };

  const verifyEmail = async (token: string): Promise<void> => {
    try {
      const response = await fetch('/api/v1/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to verify email');
      }

      // Refresh user data after successful verification
      await refreshUser();
    } catch (error) {
      throw error;
    }
  };

  const resendVerificationEmail = async (email: string): Promise<void> => {
    try {
      const response = await fetch('/api/v1/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to resend verification email');
      }
    } catch (error) {
      throw error;
    }
  };

  const value = {
    user,
    loading,
    isAdmin,
    signIn,
    signUp,
    signOut,
    refreshUser,
    changePassword,
    resetPassword,
    verifyEmail,
    resendVerificationEmail,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
