/**
 * Real-time Dashboard Hook
 * 
 * Provides real-time data updates for admin dashboard components
 * using WebSocket connections following <PERSON>'s patterns.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
// import { websocketService, LiveUpdate } from '@/services/websocketService';
import { useApiErrorHandler } from './useSimpleErrorHandler';
import { ApiService } from '@/services/apiService';

export interface DashboardStats {
  totalApplications: number;
  pendingApplications: number;
  approvedApplications: number;
  rejectedApplications: number;
  totalAgents: number;
  verifiedAgents: number;
  pendingAgents: number;
  suspendedAgents: number;
  activeThisMonth: number;
  lastUpdated: string;
}

export interface RecentActivity {
  id: string;
  type: 'application_submitted' | 'application_approved' | 'application_rejected' | 'agent_verified' | 'agent_suspended';
  title: string;
  description: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface UseRealTimeDashboardOptions {
  autoRefreshInterval?: number; // milliseconds
  enableWebSocket?: boolean;
  enablePolling?: boolean;
}

export interface UseRealTimeDashboardReturn {
  stats: DashboardStats | null;
  recentActivity: RecentActivity[];
  connectionStatus: string;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  refreshStats: () => Promise<void>;
  refreshActivity: () => Promise<void>;
  isConnected: boolean;
}

export const useRealTimeDashboard = (
  options: UseRealTimeDashboardOptions = {}
): UseRealTimeDashboardReturn => {
  const {
    autoRefreshInterval = 30000, // 30 seconds
    enableWebSocket = true,
    enablePolling = true,
  } = options;

  const { executeApiCall, isLoading, error } = useApiErrorHandler('useRealTimeDashboard');
  
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<string>('disconnected');
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isInitializedRef = useRef(false);

  // Fetch initial data
  const fetchStats = useCallback(async () => {
    const result = await executeApiCall(
      async () => {
        const [appStats, agentStats] = await Promise.all([
          ApiService.getStats(),
          ApiService.getAgentStats(),
        ]);

        return {
          totalApplications: appStats.totalApplications || 0,
          pendingApplications: appStats.pendingApplications || 0,
          approvedApplications: appStats.approvedApplications || 0,
          rejectedApplications: appStats.rejectedApplications || 0,
          totalAgents: agentStats.total || 0,
          verifiedAgents: agentStats.verified || 0,
          pendingAgents: agentStats.pending || 0,
          suspendedAgents: agentStats.suspended || 0,
          activeThisMonth: agentStats.active_this_month || 0,
          lastUpdated: new Date().toISOString(),
        };
      },
      'fetchStats',
      { retryable: true, maxRetries: 3 }
    );

    if (result) {
      setStats(result);
      setLastUpdated(new Date());
    }
  }, [executeApiCall]);

  const fetchRecentActivity = useCallback(async () => {
    const result = await executeApiCall(
      async () => {
        // In real implementation, this would be an API call
        // For now, return mock data
        const mockActivity: RecentActivity[] = [
          {
            id: '1',
            type: 'application_submitted',
            title: 'New Application',
            description: 'John Doe submitted an agent application',
            timestamp: new Date().toISOString(),
            metadata: { applicantName: 'John Doe', applicationId: 'app-123' }
          },
          {
            id: '2',
            type: 'agent_verified',
            title: 'Agent Verified',
            description: 'Sarah Johnson has been verified as an agent',
            timestamp: new Date(Date.now() - 1800000).toISOString(),
            metadata: { agentName: 'Sarah Johnson', agentId: 'agent-456' }
          },
          {
            id: '3',
            type: 'application_approved',
            title: 'Application Approved',
            description: 'Mike Wilson\'s application has been approved',
            timestamp: new Date(Date.now() - 3600000).toISOString(),
            metadata: { applicantName: 'Mike Wilson', applicationId: 'app-789' }
          },
        ];

        return mockActivity;
      },
      'fetchRecentActivity',
      { retryable: true, maxRetries: 2 }
    );

    if (result) {
      setRecentActivity(result);
    }
  }, [executeApiCall]);

  // Initialize WebSocket connection
  const initializeWebSocket = useCallback(async () => {
    if (!enableWebSocket) return;

    try {
      await websocketService.connect();
      setConnectionStatus(websocketService.getConnectionState());

      // Subscribe to relevant channels
      websocketService.subscribeToApplicationUpdates();
      websocketService.subscribeToAgentUpdates();

      // Request initial live stats
      websocketService.requestLiveStats();

      // Set up event listeners
      const handleLiveUpdate = (update: LiveUpdate) => {
        switch (update.type) {
          case 'application_status_change':
            // Update stats based on status change
            setStats(prevStats => {
              if (!prevStats) return prevStats;
              
              const { oldStatus, newStatus } = update.data;
              const newStats = { ...prevStats };
              
              // Decrement old status count
              if (oldStatus === 'pending_review') newStats.pendingApplications--;
              else if (oldStatus === 'approved') newStats.approvedApplications--;
              else if (oldStatus === 'rejected') newStats.rejectedApplications--;
              
              // Increment new status count
              if (newStatus === 'pending_review') newStats.pendingApplications++;
              else if (newStatus === 'approved') newStats.approvedApplications++;
              else if (newStatus === 'rejected') newStats.rejectedApplications++;
              
              newStats.lastUpdated = new Date().toISOString();
              return newStats;
            });

            // Add to recent activity
            setRecentActivity(prev => [{
              id: `activity-${Date.now()}`,
              type: update.data.newStatus === 'approved' ? 'application_approved' : 'application_rejected',
              title: `Application ${update.data.newStatus}`,
              description: `${update.data.applicantName}'s application has been ${update.data.newStatus}`,
              timestamp: update.timestamp,
              metadata: update.data
            }, ...prev.slice(0, 9)]);
            break;

          case 'agent_verification':
            // Update agent stats
            setStats(prevStats => {
              if (!prevStats) return prevStats;
              
              const newStats = { ...prevStats };
              if (update.data.status === 'verified') {
                newStats.verifiedAgents++;
                newStats.pendingAgents--;
              } else if (update.data.status === 'suspended') {
                newStats.suspendedAgents++;
                newStats.verifiedAgents--;
              }
              
              newStats.lastUpdated = new Date().toISOString();
              return newStats;
            });

            // Add to recent activity
            setRecentActivity(prev => [{
              id: `activity-${Date.now()}`,
              type: 'agent_verified',
              title: 'Agent Status Updated',
              description: `${update.data.agentName} has been ${update.data.status}`,
              timestamp: update.timestamp,
              metadata: update.data
            }, ...prev.slice(0, 9)]);
            break;

          case 'new_application':
            // Increment total and pending applications
            setStats(prevStats => {
              if (!prevStats) return prevStats;
              
              return {
                ...prevStats,
                totalApplications: prevStats.totalApplications + 1,
                pendingApplications: prevStats.pendingApplications + 1,
                lastUpdated: new Date().toISOString(),
              };
            });

            // Add to recent activity
            setRecentActivity(prev => [{
              id: `activity-${Date.now()}`,
              type: 'application_submitted',
              title: 'New Application',
              description: `${update.data.applicantName} submitted a new application`,
              timestamp: update.timestamp,
              metadata: update.data
            }, ...prev.slice(0, 9)]);
            break;
        }

        setLastUpdated(new Date());
      };

      const handleConnectionChange = () => {
        setConnectionStatus(websocketService.getConnectionState());
      };

      websocketService.on('liveUpdate', handleLiveUpdate);
      websocketService.on('connected', handleConnectionChange);
      websocketService.on('disconnected', handleConnectionChange);

      return () => {
        websocketService.off('liveUpdate', handleLiveUpdate);
        websocketService.off('connected', handleConnectionChange);
        websocketService.off('disconnected', handleConnectionChange);
      };
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
      setConnectionStatus('error');
    }
  }, [enableWebSocket]);

  // Set up polling for fallback
  const setupPolling = useCallback(() => {
    if (!enablePolling) return;

    refreshIntervalRef.current = setInterval(() => {
      if (!websocketService.isConnected()) {
        fetchStats();
        fetchRecentActivity();
      }
    }, autoRefreshInterval);

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
  }, [enablePolling, autoRefreshInterval, fetchStats, fetchRecentActivity]);

  // Initialize everything
  useEffect(() => {
    if (isInitializedRef.current) return;
    isInitializedRef.current = true;

    const initialize = async () => {
      // Fetch initial data
      await Promise.all([
        fetchStats(),
        fetchRecentActivity(),
      ]);

      // Initialize WebSocket
      await initializeWebSocket();

      // Set up polling as fallback
      setupPolling();
    };

    initialize();

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, [fetchStats, fetchRecentActivity, initializeWebSocket, setupPolling]);

  // Manual refresh functions
  const refreshStats = useCallback(async () => {
    await fetchStats();
  }, [fetchStats]);

  const refreshActivity = useCallback(async () => {
    await fetchRecentActivity();
  }, [fetchRecentActivity]);

  return {
    stats,
    recentActivity,
    connectionStatus,
    isLoading,
    error,
    lastUpdated,
    refreshStats,
    refreshActivity,
    isConnected: connectionStatus === 'connected',
  };
};
