/**
 * Optimized Data Table Component
 * 
 * High-performance data table with virtual scrolling, sorting,
 * filtering, and selection capabilities.
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import OptimizedPagination from './OptimizedPagination';
import { useDebounce, useVirtualScrolling } from '@/utils/performanceOptimizer';
import { 
  Search, 
  Filter, 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown,
  Download,
  RefreshCw
} from 'lucide-react';

export interface Column<T> {
  key: keyof T | string;
  title: string;
  sortable?: boolean;
  filterable?: boolean;
  width?: string | number;
  render?: (value: any, item: T, index: number) => React.ReactNode;
  className?: string;
}

export interface OptimizedDataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  error?: string | null;
  searchable?: boolean;
  selectable?: boolean;
  pagination?: {
    enabled: boolean;
    pageSize: number;
    showSizeSelector?: boolean;
  };
  virtualScrolling?: {
    enabled: boolean;
    itemHeight: number;
    containerHeight: number;
  };
  onSelectionChange?: (selectedItems: T[]) => void;
  onRefresh?: () => void;
  onExport?: (data: T[]) => void;
  emptyMessage?: string;
  className?: string;
  getRowId?: (item: T) => string;
}

function OptimizedDataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  error = null,
  searchable = true,
  selectable = false,
  pagination = { enabled: true, pageSize: 20, showSizeSelector: true },
  virtualScrolling,
  onSelectionChange,
  onRefresh,
  onExport,
  emptyMessage = 'No data available',
  className = '',
  getRowId = (item: T) => item.id || Math.random().toString(),
}: OptimizedDataTableProps<T>) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(pagination.pageSize);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Filter and sort data
  const processedData = useMemo(() => {
    let filtered = [...data];

    // Apply search filter
    if (debouncedSearchTerm) {
      const searchLower = debouncedSearchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        columns.some(column => {
          if (!column.filterable) return false;
          const value = item[column.key as keyof T];
          return String(value).toLowerCase().includes(searchLower);
        })
      );
    }

    // Apply sorting
    if (sortConfig) {
      filtered.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [data, debouncedSearchTerm, sortConfig, columns]);

  // Pagination
  const paginatedData = useMemo(() => {
    if (!pagination.enabled) return processedData;

    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return processedData.slice(startIndex, endIndex);
  }, [processedData, currentPage, pageSize, pagination.enabled]);

  // Virtual scrolling
  const virtualScrollData = useVirtualScrolling(
    paginatedData,
    virtualScrolling?.itemHeight || 50,
    virtualScrolling?.containerHeight || 400
  );

  const displayData = virtualScrolling?.enabled ? virtualScrollData.items : paginatedData;

  // Selection handlers
  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      const allIds = new Set(paginatedData.map(getRowId));
      setSelectedItems(allIds);
      onSelectionChange?.(paginatedData);
    } else {
      setSelectedItems(new Set());
      onSelectionChange?.([]);
    }
  }, [paginatedData, getRowId, onSelectionChange]);

  const handleSelectItem = useCallback((item: T, checked: boolean) => {
    const itemId = getRowId(item);
    const newSelected = new Set(selectedItems);

    if (checked) {
      newSelected.add(itemId);
    } else {
      newSelected.delete(itemId);
    }

    setSelectedItems(newSelected);
    
    const selectedData = paginatedData.filter(item => 
      newSelected.has(getRowId(item))
    );
    onSelectionChange?.(selectedData);
  }, [selectedItems, paginatedData, getRowId, onSelectionChange]);

  // Sorting handler
  const handleSort = useCallback((columnKey: string) => {
    setSortConfig(current => {
      if (current?.key === columnKey) {
        if (current.direction === 'asc') {
          return { key: columnKey, direction: 'desc' };
        } else {
          return null; // Remove sorting
        }
      } else {
        return { key: columnKey, direction: 'asc' };
      }
    });
  }, []);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
    setSelectedItems(new Set()); // Clear selection on page change
  }, []);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
    setSelectedItems(new Set());
  }, []);

  const getSortIcon = (columnKey: string) => {
    if (sortConfig?.key !== columnKey) {
      return <ArrowUpDown className="w-4 h-4 text-gray-400" />;
    }
    return sortConfig.direction === 'asc' 
      ? <ArrowUp className="w-4 h-4 text-blue-600" />
      : <ArrowDown className="w-4 h-4 text-blue-600" />;
  };

  const isAllSelected = selectedItems.size === paginatedData.length && paginatedData.length > 0;
  const isIndeterminate = selectedItems.size > 0 && selectedItems.size < paginatedData.length;

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Error loading data: {error}</p>
            {onRefresh && (
              <Button onClick={onRefresh} variant="outline" className="mt-2">
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {/* Header with search and actions */}
      <CardHeader>
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <CardTitle>
            Data Table ({processedData.length} items)
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {searchable && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
            )}
            
            {onRefresh && (
              <Button
                onClick={onRefresh}
                variant="outline"
                size="sm"
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            )}
            
            {onExport && (
              <Button
                onClick={() => onExport(processedData)}
                variant="outline"
                size="sm"
                disabled={loading || processedData.length === 0}
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            )}
          </div>
        </div>

        {selectedItems.size > 0 && (
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {selectedItems.size} selected
            </Badge>
            <Button
              onClick={() => {
                setSelectedItems(new Set());
                onSelectionChange?.([]);
              }}
              variant="outline"
              size="sm"
            >
              Clear selection
            </Button>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        ) : processedData.length === 0 ? (
          <div className="p-8 text-center text-gray-600">
            <p>{emptyMessage}</p>
          </div>
        ) : (
          <>
            {/* Table */}
            <div 
              className="overflow-auto"
              style={virtualScrolling?.enabled ? { height: virtualScrolling.containerHeight } : undefined}
              onScroll={virtualScrolling?.enabled ? virtualScrollData.handleScroll : undefined}
            >
              {virtualScrolling?.enabled && (
                <div style={{ height: virtualScrollData.totalHeight, position: 'relative' }}>
                  <div style={{ transform: `translateY(${virtualScrollData.offsetY}px)` }}>
              )}
              
              <table className="w-full">
                <thead className="bg-gray-50 sticky top-0 z-10">
                  <tr>
                    {selectable && (
                      <th className="w-12 p-4">
                        <Checkbox
                          checked={isAllSelected}
                          indeterminate={isIndeterminate}
                          onCheckedChange={handleSelectAll}
                        />
                      </th>
                    )}
                    {columns.map((column) => (
                      <th
                        key={String(column.key)}
                        className={`p-4 text-left font-medium text-gray-900 ${
                          column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                        } ${column.className || ''}`}
                        style={column.width ? { width: column.width } : undefined}
                        onClick={column.sortable ? () => handleSort(String(column.key)) : undefined}
                      >
                        <div className="flex items-center gap-2">
                          {column.title}
                          {column.sortable && getSortIcon(String(column.key))}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {displayData.map((item, index) => {
                    const itemId = getRowId(item);
                    const isSelected = selectedItems.has(itemId);
                    
                    return (
                      <tr
                        key={itemId}
                        className={`border-b hover:bg-gray-50 ${
                          isSelected ? 'bg-blue-50' : ''
                        }`}
                      >
                        {selectable && (
                          <td className="p-4">
                            <Checkbox
                              checked={isSelected}
                              onCheckedChange={(checked) => 
                                handleSelectItem(item, checked as boolean)
                              }
                            />
                          </td>
                        )}
                        {columns.map((column) => (
                          <td
                            key={String(column.key)}
                            className={`p-4 ${column.className || ''}`}
                          >
                            {column.render
                              ? column.render(item[column.key as keyof T], item, index)
                              : String(item[column.key as keyof T] || '')
                            }
                          </td>
                        ))}
                      </tr>
                    );
                  })}
                </tbody>
              </table>

              {virtualScrolling?.enabled && (
                  </div>
                </div>
              )}
            </div>

            {/* Pagination */}
            {pagination.enabled && (
              <div className="p-4 border-t">
                <OptimizedPagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(processedData.length / pageSize)}
                  totalItems={processedData.length}
                  itemsPerPage={pageSize}
                  onPageChange={handlePageChange}
                  onItemsPerPageChange={pagination.showSizeSelector ? handlePageSizeChange : undefined}
                  showItemsPerPage={pagination.showSizeSelector}
                  loading={loading}
                />
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}

export default OptimizedDataTable;
