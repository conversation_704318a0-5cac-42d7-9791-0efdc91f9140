
import { useState, useEffect } from 'react';
import { ApiService } from '@/services/apiService';
import { useToast } from '@/hooks/use-toast';
import { useRetry } from '@/hooks/useRetry';
import { useOptimizedFetch } from '@/utils/performanceOptimizer';
import { useApiErrorHandler } from '@/hooks/useSimpleErrorHandler';
import { Database } from '@/integrations/supabase/types';

type ApplicationStatus = Database['public']['Enums']['application_status'];

interface Application {
  id: string;
  agent_id: string;
  full_name: string;
  whatsapp_number: string;
  email: string;
  status: ApplicationStatus;
  created_at: string;
  operating_areas: string[];
  residential_address: string;
  is_registered_business: boolean;
  reviewer_notes?: string;
  next_action?: string;
  referee_verifications?: {
    status: string;
    referee_full_name: string;
    referee_whatsapp_number: string;
    referee_role: string;
  }[];
}

export const useApplicationsData = () => {
  const { executeApiCall, isLoading, error, clearError } = useApiErrorHandler('useApplicationsData');

  // Use optimized fetch with caching
  const {
    data: applications,
    loading,
    error: fetchError,
    refetch,
  } = useOptimizedFetch<Application[]>(
    async () => {
      // Try to fetch from API first
      try {
        const response = await ApiService.getApplications();
        return response.data || [];
      } catch (apiError) {
        console.warn('API fetch failed, falling back to mock data:', apiError);

        // Fallback to mock data
        const mockApplications: Application[] = [
          {
            id: '1',
            agent_id: 'AGT001',
            full_name: 'John Doe',
            whatsapp_number: '+2348012345678',
            email: '<EMAIL>',
            status: 'pending_review',
            created_at: new Date().toISOString(),
            operating_areas: ['Victoria Island', 'Ikoyi'],
            residential_address: '123 Main Street, Lagos',
            is_registered_business: true,
            reviewer_notes: 'Initial review pending',
            next_action: 'Document verification required',
            referee_verifications: [
              {
                status: 'verified',
                referee_full_name: 'Jane Smith',
                referee_whatsapp_number: '+2348087654321',
                referee_role: 'Previous Employer'
              }
            ]
          },
          {
            id: '2',
            agent_id: 'AGT002',
            full_name: 'Sarah Johnson',
            whatsapp_number: '+*************',
            email: '<EMAIL>',
            status: 'approved',
            created_at: new Date(Date.now() - ********).toISOString(),
            operating_areas: ['Lekki', 'Ajah'],
            residential_address: '456 Oak Avenue, Lagos',
            is_registered_business: false,
            reviewer_notes: 'All documents verified',
            next_action: 'Account setup',
            referee_verifications: [
              {
                status: 'verified',
                referee_full_name: 'Mike Wilson',
                referee_whatsapp_number: '+*************',
                referee_role: 'Business Partner'
              }
            ]
          },
          {
            id: '3',
            agent_id: 'AGT003',
            full_name: 'David Brown',
            whatsapp_number: '+*************',
            email: '<EMAIL>',
            status: 'rejected',
            created_at: new Date(Date.now() - *********).toISOString(),
            operating_areas: ['Surulere'],
            residential_address: '789 Pine Street, Lagos',
            is_registered_business: true,
            reviewer_notes: 'Incomplete documentation',
            next_action: 'Resubmit required documents',
            referee_verifications: [
              {
                status: 'pending',
                referee_full_name: 'Lisa Davis',
                referee_whatsapp_number: '+2348054321098',
                referee_role: 'Character Reference'
              }
            ]
          }
        ];

        return mockApplications;
      }
    },
    'applications-list',
    [], // dependencies
    {
      ttl: 2 * 60 * 1000, // 2 minutes cache
      retryAttempts: 3,
      retryDelay: 1000,
      enabled: true,
    }
  );

  return {
    applications: applications || [],
    loading: loading || isLoading,
    error: error || fetchError,
    refetch: () => {
      clearError();
      refetch();
    }
  };
};
