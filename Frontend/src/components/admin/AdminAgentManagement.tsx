import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ApiService } from '@/services/apiService';
import { useToast } from '@/hooks/use-toast';
import { useApiErrorHandler } from '@/hooks/useErrorHandler';
import { 
  Users, 
  Search, 
  Filter, 
  Download, 
  UserCheck, 
  UserX, 
  Eye,
  MoreHorizontal,
  TrendingUp,
  MapPin,
  Phone,
  Mail,
  Calendar,
  DollarSign,
  Building,
  Star,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AgentProfile, AgentPerformanceMetrics } from '@/types/agent';

interface AgentWithMetrics extends AgentProfile {
  performance?: AgentPerformanceMetrics;
  verification_status?: 'pending' | 'verified' | 'rejected' | 'suspended';
  last_activity?: string;
  total_properties?: number;
  total_earnings?: number;
  client_rating?: number;
}

const AdminAgentManagement = () => {
  const { toast } = useToast();
  const { executeApiCall, isLoading, error, clearError } = useApiErrorHandler('AdminAgentManagement');
  const [agents, setAgents] = useState<AgentWithMetrics[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [locationFilter, setLocationFilter] = useState<string>('all');
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [stats, setStats] = useState({
    total: 0,
    verified: 0,
    pending: 0,
    suspended: 0,
    active_this_month: 0
  });

  useEffect(() => {
    fetchAgents();
    fetchStats();
  }, [currentPage, searchTerm, statusFilter, locationFilter]);

  const fetchAgents = async () => {
    setLoading(true);
    clearError();

    const filters = {
      page: currentPage,
      limit: 20,
      search: searchTerm || undefined,
      status: statusFilter !== 'all' ? statusFilter : undefined,
      location: locationFilter !== 'all' ? locationFilter : undefined,
    };

    const response = await executeApiCall(
      () => ApiService.getAgents(filters),
      'fetchAgents',
      {
        customMessage: 'Failed to load agents. Showing sample data.',
        retryable: true,
        maxRetries: 2
      }
    );

    if (response) {
      // Transform API response to match our interface
      const agentsWithMetrics: AgentWithMetrics[] = response.data.map((agent: any) => ({
        ...agent,
        verification_status: agent.status || 'pending',
        last_activity: agent.last_login || agent.updated_at,
        total_properties: agent.properties_count || 0,
        total_earnings: agent.total_earnings || 0,
        client_rating: agent.average_rating || 0,
      }));

      setAgents(agentsWithMetrics);
      setTotalPages(Math.ceil(response.total / 20));
    } else {
      // Fallback to mock data when API fails
      const mockAgents: AgentWithMetrics[] = [
        {
          id: '1',
          agent_id: 'AGT001',
          full_name: 'John Doe',
          email: '<EMAIL>',
          whatsapp_number: '+2348012345678',
          operating_areas: ['Victoria Island', 'Ikoyi'],
          agent_status: 'active',
          verification_status: 'verified',
          last_activity: new Date().toISOString(),
          total_properties: 25,
          total_earnings: 2500000,
          client_rating: 4.8,
          created_at: '2024-01-15T00:00:00Z',
          updated_at: new Date().toISOString(),
        },
        {
          id: '2',
          agent_id: 'AGT002',
          full_name: 'Jane Smith',
          email: '<EMAIL>',
          whatsapp_number: '+2348087654321',
          operating_areas: ['Lekki', 'Ajah'],
          agent_status: 'active',
          verification_status: 'pending',
          last_activity: new Date(Date.now() - 86400000).toISOString(),
          total_properties: 18,
          total_earnings: 1800000,
          client_rating: 4.6,
          created_at: '2024-02-01T00:00:00Z',
          updated_at: new Date().toISOString(),
        },
        {
          id: '3',
          agent_id: 'AGT003',
          full_name: 'Mike Johnson',
          email: '<EMAIL>',
          whatsapp_number: '+2348098765432',
          operating_areas: ['Surulere', 'Yaba'],
          agent_status: 'inactive',
          verification_status: 'suspended',
          last_activity: new Date(Date.now() - 604800000).toISOString(),
          total_properties: 12,
          total_earnings: 950000,
          client_rating: 4.2,
          created_at: '2024-01-20T00:00:00Z',
          updated_at: new Date().toISOString(),
        }
      ];

      setAgents(mockAgents);
      setTotalPages(1);
    }

    setLoading(false);
  };

  const fetchStats = async () => {
    const statsResponse = await executeApiCall(
      () => ApiService.getAgentStats(),
      'fetchStats',
      { customMessage: 'Failed to load statistics' }
    );

    if (statsResponse) {
      setStats(statsResponse);
    } else {
      // Fallback stats
      setStats({
        total: agents.length,
        verified: agents.filter(a => a.verification_status === 'verified').length,
        pending: agents.filter(a => a.verification_status === 'pending').length,
        suspended: agents.filter(a => a.verification_status === 'suspended').length,
        active_this_month: agents.filter(a =>
          new Date(a.last_activity || '').getTime() > Date.now() - 30 * 24 * 60 * 60 * 1000
        ).length
      });
    }
  };

  const handleStatusChange = async (agentId: string, newStatus: string) => {
    const result = await executeApiCall(
      () => ApiService.updateAgentStatus(agentId, newStatus),
      'updateAgentStatus',
      { customMessage: 'Failed to update agent status' }
    );

    if (result !== null) {
      setAgents(prev => prev.map(agent =>
        agent.id === agentId
          ? { ...agent, verification_status: newStatus as any }
          : agent
      ));

      toast({
        title: 'Status Updated',
        description: `Agent status has been updated to ${newStatus}.`,
      });

      fetchStats(); // Refresh stats
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedAgents.length === 0) {
      toast({
        title: 'No Selection',
        description: 'Please select agents to perform bulk actions.',
        variant: 'destructive',
      });
      return;
    }

    try {
      await ApiService.bulkUpdateAgents(selectedAgents, { action });
      
      toast({
        title: 'Bulk Action Completed',
        description: `${action} applied to ${selectedAgents.length} agents.`,
      });

      setSelectedAgents([]);
      fetchAgents();
    } catch (error) {
      toast({
        title: 'Bulk Action Failed',
        description: 'Failed to perform bulk action.',
        variant: 'destructive',
      });
    }
  };

  const exportAgents = async () => {
    try {
      const exportData = agents.map(agent => ({
        'Agent ID': agent.agent_id,
        'Full Name': agent.full_name,
        'Email': agent.email,
        'Phone': agent.whatsapp_number,
        'Operating Areas': agent.operating_areas.join(', '),
        'Status': agent.verification_status,
        'Properties': agent.total_properties,
        'Earnings': agent.total_earnings,
        'Rating': agent.client_rating,
        'Last Activity': new Date(agent.last_activity || '').toLocaleDateString(),
      }));

      const csv = [
        Object.keys(exportData[0]).join(','),
        ...exportData.map(row => Object.values(row).join(','))
      ].join('\n');

      const blob = new Blob([csv], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `agents-export-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Export Successful',
        description: 'Agent data has been exported to CSV.',
      });
    } catch (error) {
      toast({
        title: 'Export Failed',
        description: 'Failed to export agent data.',
        variant: 'destructive',
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      verified: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      rejected: { color: 'bg-red-100 text-red-800', icon: XCircle },
      suspended: { color: 'bg-gray-100 text-gray-800', icon: AlertCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = agent.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         agent.agent_id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || agent.verification_status === statusFilter;
    
    const matchesLocation = locationFilter === 'all' || 
                           agent.operating_areas.some(area => 
                             area.toLowerCase().includes(locationFilter.toLowerCase())
                           );

    return matchesSearch && matchesStatus && matchesLocation;
  });

  return (
    <div className="space-y-6 p-6">
      {/* Header with Stats */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Agent Management</h2>
          <p className="text-gray-600">Manage and monitor all verified agents</p>
        </div>
        
        <div className="flex items-center gap-4">
          <Button onClick={exportAgents} variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Agents</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Verified</p>
                <p className="text-2xl font-bold text-green-600">{stats.verified}</p>
              </div>
              <UserCheck className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Suspended</p>
                <p className="text-2xl font-bold text-red-600">{stats.suspended}</p>
              </div>
              <UserX className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active This Month</p>
                <p className="text-2xl font-bold text-purple-600">{stats.active_this_month}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search agents by name, email, or ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>

              <Select value={locationFilter} onValueChange={setLocationFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  <SelectItem value="victoria island">Victoria Island</SelectItem>
                  <SelectItem value="ikoyi">Ikoyi</SelectItem>
                  <SelectItem value="lekki">Lekki</SelectItem>
                  <SelectItem value="ajah">Ajah</SelectItem>
                  <SelectItem value="surulere">Surulere</SelectItem>
                  <SelectItem value="yaba">Yaba</SelectItem>
                </SelectContent>
              </Select>

              {selectedAgents.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline">
                      Bulk Actions ({selectedAgents.length})
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuLabel>Bulk Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleBulkAction('verify')}>
                      <UserCheck className="w-4 h-4 mr-2" />
                      Verify Selected
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkAction('suspend')}>
                      <UserX className="w-4 h-4 mr-2" />
                      Suspend Selected
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkAction('export')}>
                      <Download className="w-4 h-4 mr-2" />
                      Export Selected
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Agents Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Agents ({filteredAgents.length})</span>
            {loading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map(i => (
                <div key={i} className="animate-pulse">
                  <div className="flex items-center space-x-4 p-4 border rounded-lg">
                    <div className="w-4 h-4 bg-gray-200 rounded"></div>
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                    </div>
                    <div className="w-20 h-6 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredAgents.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No agents found</h3>
              <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAgents.map((agent) => (
                <div key={agent.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedAgents.includes(agent.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedAgents(prev => [...prev, agent.id]);
                        } else {
                          setSelectedAgents(prev => prev.filter(id => id !== agent.id));
                        }
                      }}
                      className="w-4 h-4 text-blue-600 rounded"
                    />

                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                      {agent.full_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {agent.full_name}
                        </h3>
                        <span className="text-xs text-gray-500">({agent.agent_id})</span>
                        {getStatusBadge(agent.verification_status || 'pending')}
                      </div>

                      <div className="flex items-center gap-4 text-xs text-gray-600">
                        <div className="flex items-center gap-1">
                          <Mail className="w-3 h-3" />
                          {agent.email}
                        </div>
                        <div className="flex items-center gap-1">
                          <Phone className="w-3 h-3" />
                          {agent.whatsapp_number}
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {agent.operating_areas.slice(0, 2).join(', ')}
                          {agent.operating_areas.length > 2 && ` +${agent.operating_areas.length - 2}`}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-6 text-sm">
                      <div className="text-center">
                        <div className="flex items-center gap-1 text-gray-600">
                          <Building className="w-4 h-4" />
                          <span>{agent.total_properties || 0}</span>
                        </div>
                        <p className="text-xs text-gray-500">Properties</p>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center gap-1 text-gray-600">
                          <DollarSign className="w-4 h-4" />
                          <span>₦{((agent.total_earnings || 0) / 1000000).toFixed(1)}M</span>
                        </div>
                        <p className="text-xs text-gray-500">Earnings</p>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center gap-1 text-gray-600">
                          <Star className="w-4 h-4" />
                          <span>{(agent.client_rating || 0).toFixed(1)}</span>
                        </div>
                        <p className="text-xs text-gray-500">Rating</p>
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleStatusChange(agent.id, 'verified')}>
                          <UserCheck className="w-4 h-4 mr-2" />
                          Verify Agent
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleStatusChange(agent.id, 'suspended')}>
                          <UserX className="w-4 h-4 mr-2" />
                          Suspend Agent
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <XCircle className="w-4 h-4 mr-2" />
                          Reject Agent
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, filteredAgents.length)} of {filteredAgents.length} agents
          </p>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>

            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                return (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                  >
                    {page}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminAgentManagement;
