/**
 * Test Suite for AdminAgentManagement Component
 * 
 * Comprehensive tests covering functionality, error handling,
 * and user interactions following <PERSON>'s testing patterns.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AdminAgentManagement from '../AdminAgentManagement';
import { ApiService } from '@/services/apiService';
import { useToast } from '@/hooks/use-toast';

// Mock dependencies
vi.mock('@/services/apiService');
vi.mock('@/hooks/use-toast');

// Mock data
const mockAgents = [
  {
    id: '1',
    agent_id: 'AGT001',
    full_name: '<PERSON>',
    email: '<EMAIL>',
    whatsapp_number: '+2348012345678',
    operating_areas: ['Victoria Island', 'Ikoyi'],
    agent_status: 'active',
    verification_status: 'verified',
    last_activity: new Date().toISOString(),
    total_properties: 25,
    total_earnings: 2500000,
    client_rating: 4.8,
    created_at: '2024-01-15T00:00:00Z',
    updated_at: new Date().toISOString(),
  },
  {
    id: '2',
    agent_id: 'AGT002',
    full_name: 'Jane Smith',
    email: '<EMAIL>',
    whatsapp_number: '+2348087654321',
    operating_areas: ['Lekki', 'Ajah'],
    agent_status: 'active',
    verification_status: 'pending',
    last_activity: new Date(Date.now() - 86400000).toISOString(),
    total_properties: 18,
    total_earnings: 1800000,
    client_rating: 4.6,
    created_at: '2024-02-01T00:00:00Z',
    updated_at: new Date().toISOString(),
  },
];

const mockStats = {
  total: 2,
  verified: 1,
  pending: 1,
  suspended: 0,
  active_this_month: 2,
};

// Test utilities
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

const mockToast = vi.fn();

describe('AdminAgentManagement', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useToast as any).mockReturnValue({ toast: mockToast });
    
    // Default API mocks
    (ApiService.getAgents as any).mockResolvedValue({
      data: mockAgents,
      total: mockAgents.length,
    });
    (ApiService.getAgentStats as any).mockResolvedValue(mockStats);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders the component with correct title and stats', async () => {
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      expect(screen.getByText('Agent Management')).toBeInTheDocument();
      expect(screen.getByText('Manage and monitor all verified agents')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByText('Total Agents')).toBeInTheDocument();
        expect(screen.getByText('Verified')).toBeInTheDocument();
        expect(screen.getByText('Pending')).toBeInTheDocument();
        expect(screen.getByText('Suspended')).toBeInTheDocument();
        expect(screen.getByText('Active This Month')).toBeInTheDocument();
      });
    });

    it('displays loading state initially', () => {
      render(<AdminAgentManagement />, { wrapper: createWrapper() });
      
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('displays agents list after loading', async () => {
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
        expect(screen.getByText('AGT001')).toBeInTheDocument();
        expect(screen.getByText('AGT002')).toBeInTheDocument();
      });
    });
  });

  describe('Search and Filtering', () => {
    it('filters agents by search term', async () => {
      const user = userEvent.setup();
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('Search agents by name, email, or ID...');
      await user.type(searchInput, 'John');

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      });
    });

    it('filters agents by status', async () => {
      const user = userEvent.setup();
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });

      const statusFilter = screen.getByDisplayValue('All Status');
      await user.click(statusFilter);
      await user.click(screen.getByText('Verified'));

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      });
    });

    it('filters agents by location', async () => {
      const user = userEvent.setup();
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });

      const locationFilter = screen.getByDisplayValue('All Locations');
      await user.click(locationFilter);
      await user.click(screen.getByText('Victoria Island'));

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
      });
    });
  });

  describe('Agent Actions', () => {
    it('updates agent status successfully', async () => {
      const user = userEvent.setup();
      (ApiService.updateAgentStatus as any).mockResolvedValue({});
      
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });

      // Find and click the actions menu for Jane Smith (pending agent)
      const agentRows = screen.getAllByRole('button', { name: /more/i });
      await user.click(agentRows[1]); // Assuming Jane Smith is second

      const verifyButton = screen.getByText('Verify Agent');
      await user.click(verifyButton);

      await waitFor(() => {
        expect(ApiService.updateAgentStatus).toHaveBeenCalledWith('2', 'verified');
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Status Updated',
          description: 'Agent status has been updated to verified.',
        });
      });
    });

    it('handles status update failure', async () => {
      const user = userEvent.setup();
      (ApiService.updateAgentStatus as any).mockRejectedValue(new Error('Update failed'));
      
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });

      const agentRows = screen.getAllByRole('button', { name: /more/i });
      await user.click(agentRows[1]);

      const verifyButton = screen.getByText('Verify Agent');
      await user.click(verifyButton);

      await waitFor(() => {
        expect(ApiService.updateAgentStatus).toHaveBeenCalledWith('2', 'verified');
        // Error handling should be managed by the error handler hook
      });
    });
  });

  describe('Bulk Actions', () => {
    it('selects multiple agents and performs bulk action', async () => {
      const user = userEvent.setup();
      (ApiService.bulkUpdateAgents as any).mockResolvedValue({});
      
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });

      // Select both agents
      const checkboxes = screen.getAllByRole('checkbox');
      await user.click(checkboxes[1]); // First agent
      await user.click(checkboxes[2]); // Second agent

      // Bulk actions should now be visible
      const bulkActionsButton = screen.getByText(/Bulk Actions \(2\)/);
      await user.click(bulkActionsButton);

      const verifySelectedButton = screen.getByText('Verify Selected');
      await user.click(verifySelectedButton);

      await waitFor(() => {
        expect(ApiService.bulkUpdateAgents).toHaveBeenCalledWith(['1', '2'], { action: 'verify' });
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Bulk Action Completed',
          description: 'verify applied to 2 agents.',
        });
      });
    });

    it('shows warning when no agents selected for bulk action', async () => {
      const user = userEvent.setup();
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Try to perform bulk action without selection
      // Note: Bulk actions button should not be visible when no items selected
      expect(screen.queryByText(/Bulk Actions/)).not.toBeInTheDocument();
    });
  });

  describe('Export Functionality', () => {
    it('exports agent data to CSV', async () => {
      const user = userEvent.setup();
      
      // Mock URL.createObjectURL and related functions
      const mockCreateObjectURL = vi.fn(() => 'mock-url');
      const mockRevokeObjectURL = vi.fn();
      const mockClick = vi.fn();
      
      Object.defineProperty(window, 'URL', {
        value: {
          createObjectURL: mockCreateObjectURL,
          revokeObjectURL: mockRevokeObjectURL,
        },
      });

      // Mock document.createElement
      const mockAnchor = {
        href: '',
        download: '',
        click: mockClick,
      };
      vi.spyOn(document, 'createElement').mockReturnValue(mockAnchor as any);

      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      const exportButton = screen.getByText('Export');
      await user.click(exportButton);

      await waitFor(() => {
        expect(mockCreateObjectURL).toHaveBeenCalled();
        expect(mockClick).toHaveBeenCalled();
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Export Successful',
          description: 'Agent data has been exported to CSV.',
        });
      });
    });
  });

  describe('Error Handling', () => {
    it('handles API fetch failure gracefully', async () => {
      (ApiService.getAgents as any).mockRejectedValue(new Error('Network error'));
      (ApiService.getAgentStats as any).mockRejectedValue(new Error('Network error'));

      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      // Should fall back to mock data
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });
    });

    it('displays empty state when no agents found', async () => {
      (ApiService.getAgents as any).mockResolvedValue({
        data: [],
        total: 0,
      });
      (ApiService.getAgentStats as any).mockResolvedValue({
        total: 0,
        verified: 0,
        pending: 0,
        suspended: 0,
        active_this_month: 0,
      });

      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('No agents found')).toBeInTheDocument();
        expect(screen.getByText('Try adjusting your search or filter criteria.')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', async () => {
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Check for proper headings
      expect(screen.getByRole('heading', { name: 'Agent Management' })).toBeInTheDocument();
      
      // Check for proper form controls
      expect(screen.getByRole('textbox', { name: /search/i })).toBeInTheDocument();
      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('Search agents by name, email, or ID...');
      
      // Tab to search input and type
      await user.tab();
      expect(searchInput).toHaveFocus();
      
      await user.type(searchInput, 'John');
      expect(searchInput).toHaveValue('John');
    });
  });

  describe('Performance', () => {
    it('debounces search input', async () => {
      const user = userEvent.setup();
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText('Search agents by name, email, or ID...');
      
      // Type quickly - should not trigger multiple API calls
      await user.type(searchInput, 'John', { delay: 50 });
      
      // Only initial load should have been called
      expect(ApiService.getAgents).toHaveBeenCalledTimes(1);
    });

    it('handles large datasets efficiently', async () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        ...mockAgents[0],
        id: `agent-${i}`,
        agent_id: `AGT${String(i).padStart(3, '0')}`,
        full_name: `Agent ${i}`,
        email: `agent${i}@example.com`,
      }));

      (ApiService.getAgents as any).mockResolvedValue({
        data: largeDataset,
        total: largeDataset.length,
      });

      const startTime = performance.now();
      render(<AdminAgentManagement />, { wrapper: createWrapper() });

      await waitFor(() => {
        expect(screen.getByText('Agent 0')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (less than 2 seconds)
      expect(renderTime).toBeLessThan(2000);
    });
  });
});
