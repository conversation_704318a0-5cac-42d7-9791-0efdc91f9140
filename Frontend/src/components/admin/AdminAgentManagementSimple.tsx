/**
 * Simplified Admin Agent Management Component
 * 
 * Basic version to test if the component renders without issues.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Users, Search, Download } from 'lucide-react';

interface SimpleAgent {
  id: string;
  agent_id: string;
  full_name: string;
  email: string;
  whatsapp_number: string;
  operating_areas: string[];
  verification_status: 'verified' | 'pending' | 'rejected' | 'suspended';
  total_properties: number;
  total_earnings: number;
  client_rating: number;
}

const AdminAgentManagementSimple: React.FC = () => {
  const [agents, setAgents] = useState<SimpleAgent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    // Simulate loading data
    setTimeout(() => {
      const mockAgents: SimpleAgent[] = [
        {
          id: '1',
          agent_id: 'AGT001',
          full_name: '<PERSON> Doe',
          email: '<EMAIL>',
          whatsapp_number: '+2348012345678',
          operating_areas: ['Victoria Island', 'Ikoyi'],
          verification_status: 'verified',
          total_properties: 25,
          total_earnings: 2500000,
          client_rating: 4.8,
        },
        {
          id: '2',
          agent_id: 'AGT002',
          full_name: 'Jane Smith',
          email: '<EMAIL>',
          whatsapp_number: '+2348087654321',
          operating_areas: ['Lekki', 'Ajah'],
          verification_status: 'pending',
          total_properties: 18,
          total_earnings: 1800000,
          client_rating: 4.6,
        },
      ];
      
      setAgents(mockAgents);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      verified: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      rejected: 'bg-red-100 text-red-800',
      suspended: 'bg-gray-100 text-gray-800',
    };

    const color = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;

    return (
      <Badge className={color}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const filteredAgents = agents.filter(agent =>
    agent.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agent.agent_id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Agent Management</h2>
          <p className="text-gray-600">Manage and monitor all verified agents</p>
        </div>
        
        <div className="flex items-center gap-4">
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Agents</p>
                <p className="text-2xl font-bold">{agents.length}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Verified</p>
                <p className="text-2xl font-bold text-green-600">
                  {agents.filter(a => a.verification_status === 'verified').length}
                </p>
              </div>
              <Users className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {agents.filter(a => a.verification_status === 'pending').length}
                </p>
              </div>
              <Users className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Suspended</p>
                <p className="text-2xl font-bold text-red-600">
                  {agents.filter(a => a.verification_status === 'suspended').length}
                </p>
              </div>
              <Users className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search agents by name, email, or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Agents List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Agents ({filteredAgents.length})</span>
            {loading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3].map(i => (
                <div key={i} className="animate-pulse">
                  <div className="flex items-center space-x-4 p-4 border rounded-lg">
                    <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                    </div>
                    <div className="w-20 h-6 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredAgents.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No agents found</h3>
              <p className="text-gray-600">Try adjusting your search criteria.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAgents.map((agent) => (
                <div key={agent.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                      {agent.full_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {agent.full_name}
                        </h3>
                        <span className="text-xs text-gray-500">({agent.agent_id})</span>
                        {getStatusBadge(agent.verification_status)}
                      </div>
                      
                      <div className="flex items-center gap-4 text-xs text-gray-600">
                        <span>{agent.email}</span>
                        <span>{agent.whatsapp_number}</span>
                        <span>{agent.operating_areas.join(', ')}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-6 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{agent.total_properties}</div>
                        <p className="text-xs text-gray-500">Properties</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="font-medium">₦{(agent.total_earnings / 1000000).toFixed(1)}M</div>
                        <p className="text-xs text-gray-500">Earnings</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="font-medium">{agent.client_rating.toFixed(1)}</div>
                        <p className="text-xs text-gray-500">Rating</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminAgentManagementSimple;
