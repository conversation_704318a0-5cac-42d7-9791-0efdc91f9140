/**
 * Centralized Error Handling System
 * 
 * This module provides consistent error handling patterns across the application,
 * following <PERSON>'s established patterns for admin components.
 */

import { toast } from '@/hooks/use-toast';

// Error types that can occur in the application
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Error context for better debugging
export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp?: string;
  additionalData?: Record<string, any>;
}

// Standardized error interface
export interface AppError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  originalError?: Error;
  context?: ErrorContext;
  userMessage?: string;
  actionable?: boolean;
  retryable?: boolean;
}

// Error handling configuration
interface ErrorConfig {
  showToast: boolean;
  logToConsole: boolean;
  reportToService: boolean;
  fallbackMessage: string;
  retryable: boolean;
}

// Default error configurations for different error types
const ERROR_CONFIGS: Record<ErrorType, ErrorConfig> = {
  [ErrorType.NETWORK_ERROR]: {
    showToast: true,
    logToConsole: true,
    reportToService: true,
    fallbackMessage: 'Network connection failed. Please check your internet connection.',
    retryable: true,
  },
  [ErrorType.API_ERROR]: {
    showToast: true,
    logToConsole: true,
    reportToService: true,
    fallbackMessage: 'Server error occurred. Please try again later.',
    retryable: true,
  },
  [ErrorType.VALIDATION_ERROR]: {
    showToast: true,
    logToConsole: false,
    reportToService: false,
    fallbackMessage: 'Please check your input and try again.',
    retryable: false,
  },
  [ErrorType.AUTHENTICATION_ERROR]: {
    showToast: true,
    logToConsole: true,
    reportToService: true,
    fallbackMessage: 'Authentication failed. Please log in again.',
    retryable: false,
  },
  [ErrorType.AUTHORIZATION_ERROR]: {
    showToast: true,
    logToConsole: true,
    reportToService: true,
    fallbackMessage: 'You do not have permission to perform this action.',
    retryable: false,
  },
  [ErrorType.NOT_FOUND_ERROR]: {
    showToast: true,
    logToConsole: false,
    reportToService: false,
    fallbackMessage: 'The requested resource was not found.',
    retryable: false,
  },
  [ErrorType.SERVER_ERROR]: {
    showToast: true,
    logToConsole: true,
    reportToService: true,
    fallbackMessage: 'Internal server error. Please try again later.',
    retryable: true,
  },
  [ErrorType.TIMEOUT_ERROR]: {
    showToast: true,
    logToConsole: true,
    reportToService: true,
    fallbackMessage: 'Request timed out. Please try again.',
    retryable: true,
  },
  [ErrorType.UNKNOWN_ERROR]: {
    showToast: true,
    logToConsole: true,
    reportToService: true,
    fallbackMessage: 'An unexpected error occurred. Please try again.',
    retryable: true,
  },
};

/**
 * Main error handler class
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorQueue: AppError[] = [];
  private isReporting = false;

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle an error with full context
   */
  handleError(error: AppError): void {
    const config = ERROR_CONFIGS[error.type];
    
    // Add timestamp if not provided
    if (error.context && !error.context.timestamp) {
      error.context.timestamp = new Date().toISOString();
    }

    // Show user notification
    if (config.showToast) {
      this.showUserNotification(error, config);
    }

    // Log to console for development
    if (config.logToConsole) {
      this.logError(error);
    }

    // Report to error tracking service
    if (config.reportToService) {
      this.reportError(error);
    }
  }

  /**
   * Handle API errors specifically
   */
  handleApiError(
    error: any,
    context: ErrorContext,
    customMessage?: string
  ): AppError {
    const appError = this.parseApiError(error, context, customMessage);
    this.handleError(appError);
    return appError;
  }

  /**
   * Handle network errors
   */
  handleNetworkError(
    error: Error,
    context: ErrorContext,
    customMessage?: string
  ): AppError {
    const appError: AppError = {
      type: ErrorType.NETWORK_ERROR,
      severity: ErrorSeverity.HIGH,
      message: error.message,
      originalError: error,
      context,
      userMessage: customMessage || ERROR_CONFIGS[ErrorType.NETWORK_ERROR].fallbackMessage,
      retryable: true,
    };

    this.handleError(appError);
    return appError;
  }

  /**
   * Handle validation errors
   */
  handleValidationError(
    message: string,
    context: ErrorContext,
    fieldErrors?: Record<string, string>
  ): AppError {
    const appError: AppError = {
      type: ErrorType.VALIDATION_ERROR,
      severity: ErrorSeverity.MEDIUM,
      message,
      context: {
        ...context,
        additionalData: { fieldErrors },
      },
      userMessage: message,
      retryable: false,
    };

    this.handleError(appError);
    return appError;
  }

  /**
   * Parse API errors into standardized format
   */
  private parseApiError(
    error: any,
    context: ErrorContext,
    customMessage?: string
  ): AppError {
    let errorType = ErrorType.API_ERROR;
    let severity = ErrorSeverity.MEDIUM;
    let message = 'API request failed';
    let userMessage = customMessage;

    // Parse different error formats
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;

      switch (status) {
        case 400:
          errorType = ErrorType.VALIDATION_ERROR;
          severity = ErrorSeverity.MEDIUM;
          message = data?.message || 'Invalid request data';
          break;
        case 401:
          errorType = ErrorType.AUTHENTICATION_ERROR;
          severity = ErrorSeverity.HIGH;
          message = 'Authentication required';
          break;
        case 403:
          errorType = ErrorType.AUTHORIZATION_ERROR;
          severity = ErrorSeverity.HIGH;
          message = 'Access denied';
          break;
        case 404:
          errorType = ErrorType.NOT_FOUND_ERROR;
          severity = ErrorSeverity.LOW;
          message = 'Resource not found';
          break;
        case 408:
          errorType = ErrorType.TIMEOUT_ERROR;
          severity = ErrorSeverity.MEDIUM;
          message = 'Request timeout';
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          errorType = ErrorType.SERVER_ERROR;
          severity = ErrorSeverity.HIGH;
          message = 'Server error';
          break;
        default:
          message = data?.message || `HTTP ${status} error`;
      }

      userMessage = userMessage || data?.userMessage || ERROR_CONFIGS[errorType].fallbackMessage;
    } else if (error.request) {
      errorType = ErrorType.NETWORK_ERROR;
      severity = ErrorSeverity.HIGH;
      message = 'Network request failed';
      userMessage = userMessage || ERROR_CONFIGS[ErrorType.NETWORK_ERROR].fallbackMessage;
    } else {
      errorType = ErrorType.UNKNOWN_ERROR;
      severity = ErrorSeverity.MEDIUM;
      message = error.message || 'Unknown error occurred';
      userMessage = userMessage || ERROR_CONFIGS[ErrorType.UNKNOWN_ERROR].fallbackMessage;
    }

    return {
      type: errorType,
      severity,
      message,
      originalError: error,
      context,
      userMessage,
      retryable: ERROR_CONFIGS[errorType].retryable,
    };
  }

  /**
   * Show user-friendly notification
   */
  private showUserNotification(error: AppError, config: ErrorConfig): void {
    const variant = error.severity === ErrorSeverity.CRITICAL || error.severity === ErrorSeverity.HIGH 
      ? 'destructive' 
      : 'default';

    toast({
      title: this.getErrorTitle(error.type),
      description: error.userMessage || config.fallbackMessage,
      variant,
      duration: error.severity === ErrorSeverity.CRITICAL ? 10000 : 5000,
    });
  }

  /**
   * Log error to console
   */
  private logError(error: AppError): void {
    const logLevel = error.severity === ErrorSeverity.CRITICAL || error.severity === ErrorSeverity.HIGH 
      ? 'error' 
      : 'warn';

    console[logLevel]('Application Error:', {
      type: error.type,
      severity: error.severity,
      message: error.message,
      context: error.context,
      originalError: error.originalError,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Report error to external service
   */
  private async reportError(error: AppError): Promise<void> {
    // Add to queue for batch reporting
    this.errorQueue.push(error);

    // Process queue if not already processing
    if (!this.isReporting && this.errorQueue.length > 0) {
      this.processErrorQueue();
    }
  }

  /**
   * Process error queue for batch reporting
   */
  private async processErrorQueue(): Promise<void> {
    if (this.isReporting || this.errorQueue.length === 0) return;

    this.isReporting = true;

    try {
      // In a real application, you would send errors to a service like Sentry
      // For now, we'll just log them
      console.log('Reporting errors to service:', this.errorQueue);
      
      // Clear the queue after successful reporting
      this.errorQueue = [];
    } catch (reportingError) {
      console.error('Failed to report errors:', reportingError);
    } finally {
      this.isReporting = false;
    }
  }

  /**
   * Get user-friendly error title
   */
  private getErrorTitle(errorType: ErrorType): string {
    const titles: Record<ErrorType, string> = {
      [ErrorType.NETWORK_ERROR]: 'Connection Error',
      [ErrorType.API_ERROR]: 'Server Error',
      [ErrorType.VALIDATION_ERROR]: 'Validation Error',
      [ErrorType.AUTHENTICATION_ERROR]: 'Authentication Error',
      [ErrorType.AUTHORIZATION_ERROR]: 'Access Denied',
      [ErrorType.NOT_FOUND_ERROR]: 'Not Found',
      [ErrorType.SERVER_ERROR]: 'Server Error',
      [ErrorType.TIMEOUT_ERROR]: 'Request Timeout',
      [ErrorType.UNKNOWN_ERROR]: 'Unexpected Error',
    };

    return titles[errorType] || 'Error';
  }

  /**
   * Create retry function for retryable errors
   */
  createRetryFunction(
    originalFunction: () => Promise<any>,
    context: ErrorContext,
    maxRetries = 3,
    delay = 1000
  ): () => Promise<any> {
    return async () => {
      let lastError: AppError | null = null;
      
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await originalFunction();
        } catch (error) {
          lastError = this.handleApiError(error, {
            ...context,
            additionalData: { attempt, maxRetries },
          });

          if (!lastError.retryable || attempt === maxRetries) {
            throw lastError;
          }

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
      }

      throw lastError;
    };
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Convenience functions for common error scenarios
export const handleApiError = (error: any, context: ErrorContext, customMessage?: string) =>
  errorHandler.handleApiError(error, context, customMessage);

export const handleNetworkError = (error: Error, context: ErrorContext, customMessage?: string) =>
  errorHandler.handleNetworkError(error, context, customMessage);

export const handleValidationError = (
  message: string,
  context: ErrorContext,
  fieldErrors?: Record<string, string>
) => errorHandler.handleValidationError(message, context, fieldErrors);

export const createRetryFunction = (
  originalFunction: () => Promise<any>,
  context: ErrorContext,
  maxRetries?: number,
  delay?: number
) => errorHandler.createRetryFunction(originalFunction, context, maxRetries, delay);
