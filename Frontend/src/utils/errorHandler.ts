/**
 * Simplified <PERSON>rror Handling System
 * 
 * Essential error handling utilities for immediate use.
 */

// Error types that can occur in the application
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Error context for better debugging
export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp?: string;
  additionalData?: Record<string, any>;
}

// Standardized error interface
export interface AppError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  originalError?: Error;
  context?: ErrorContext;
  userMessage?: string;
  actionable?: boolean;
  retryable?: boolean;
}

// Simple error handler class
export class ErrorHandler {
  private static instance: ErrorHandler;

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  handleError(error: AppError): void {
    // Add timestamp if not provided
    if (error.context && !error.context.timestamp) {
      error.context.timestamp = new Date().toISOString();
    }

    // Log to console
    console.error('Application Error:', {
      type: error.type,
      severity: error.severity,
      message: error.message,
      context: error.context,
      originalError: error.originalError,
      timestamp: new Date().toISOString(),
    });

    // Show user notification (simplified)
    if (error.userMessage) {
      console.log('User notification:', error.userMessage);
    }
  }

  handleApiError(
    error: any,
    context: ErrorContext,
    customMessage?: string
  ): AppError {
    const appError = this.parseApiError(error, context, customMessage);
    this.handleError(appError);
    return appError;
  }

  handleNetworkError(
    error: Error,
    context: ErrorContext,
    customMessage?: string
  ): AppError {
    const appError: AppError = {
      type: ErrorType.NETWORK_ERROR,
      severity: ErrorSeverity.HIGH,
      message: error.message,
      originalError: error,
      context,
      userMessage: customMessage || 'Network connection failed. Please check your internet connection.',
      retryable: true,
    };

    this.handleError(appError);
    return appError;
  }

  handleValidationError(
    message: string,
    context: ErrorContext,
    fieldErrors?: Record<string, string>
  ): AppError {
    const appError: AppError = {
      type: ErrorType.VALIDATION_ERROR,
      severity: ErrorSeverity.MEDIUM,
      message,
      context: {
        ...context,
        additionalData: { fieldErrors },
      },
      userMessage: message,
      retryable: false,
    };

    this.handleError(appError);
    return appError;
  }

  private parseApiError(
    error: any,
    context: ErrorContext,
    customMessage?: string
  ): AppError {
    let errorType = ErrorType.API_ERROR;
    let severity = ErrorSeverity.MEDIUM;
    let message = 'API request failed';
    let userMessage = customMessage;

    // Parse different error formats
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;

      switch (status) {
        case 400:
          errorType = ErrorType.VALIDATION_ERROR;
          severity = ErrorSeverity.MEDIUM;
          message = data?.message || 'Invalid request data';
          break;
        case 401:
          errorType = ErrorType.AUTHENTICATION_ERROR;
          severity = ErrorSeverity.HIGH;
          message = 'Authentication required';
          break;
        case 403:
          errorType = ErrorType.AUTHORIZATION_ERROR;
          severity = ErrorSeverity.HIGH;
          message = 'Access denied';
          break;
        case 404:
          errorType = ErrorType.NOT_FOUND_ERROR;
          severity = ErrorSeverity.LOW;
          message = 'Resource not found';
          break;
        case 408:
          errorType = ErrorType.TIMEOUT_ERROR;
          severity = ErrorSeverity.MEDIUM;
          message = 'Request timeout';
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          errorType = ErrorType.SERVER_ERROR;
          severity = ErrorSeverity.HIGH;
          message = 'Server error';
          break;
        default:
          message = data?.message || `HTTP ${status} error`;
      }

      userMessage = userMessage || data?.userMessage || this.getDefaultMessage(errorType);
    } else if (error.request) {
      errorType = ErrorType.NETWORK_ERROR;
      severity = ErrorSeverity.HIGH;
      message = 'Network request failed';
      userMessage = userMessage || 'Network connection failed. Please check your internet connection.';
    } else {
      errorType = ErrorType.UNKNOWN_ERROR;
      severity = ErrorSeverity.MEDIUM;
      message = error.message || 'Unknown error occurred';
      userMessage = userMessage || 'An unexpected error occurred. Please try again.';
    }

    return {
      type: errorType,
      severity,
      message,
      originalError: error,
      context,
      userMessage,
      retryable: this.isRetryable(errorType),
    };
  }

  private getDefaultMessage(errorType: ErrorType): string {
    const messages: Record<ErrorType, string> = {
      [ErrorType.NETWORK_ERROR]: 'Network connection failed. Please check your internet connection.',
      [ErrorType.API_ERROR]: 'Server error occurred. Please try again later.',
      [ErrorType.VALIDATION_ERROR]: 'Please check your input and try again.',
      [ErrorType.AUTHENTICATION_ERROR]: 'Authentication failed. Please log in again.',
      [ErrorType.AUTHORIZATION_ERROR]: 'You do not have permission to perform this action.',
      [ErrorType.NOT_FOUND_ERROR]: 'The requested resource was not found.',
      [ErrorType.SERVER_ERROR]: 'Internal server error. Please try again later.',
      [ErrorType.TIMEOUT_ERROR]: 'Request timed out. Please try again.',
      [ErrorType.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.',
    };

    return messages[errorType] || 'An error occurred.';
  }

  private isRetryable(errorType: ErrorType): boolean {
    const retryableTypes = [
      ErrorType.NETWORK_ERROR,
      ErrorType.API_ERROR,
      ErrorType.SERVER_ERROR,
      ErrorType.TIMEOUT_ERROR,
      ErrorType.UNKNOWN_ERROR,
    ];

    return retryableTypes.includes(errorType);
  }

  createRetryFunction(
    originalFunction: () => Promise<any>,
    context: ErrorContext,
    maxRetries = 3,
    delay = 1000
  ): () => Promise<any> {
    return async () => {
      let lastError: AppError | null = null;
      
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await originalFunction();
        } catch (error) {
          lastError = this.handleApiError(error, {
            ...context,
            additionalData: { attempt, maxRetries },
          });

          if (!lastError.retryable || attempt === maxRetries) {
            throw lastError;
          }

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
      }

      throw lastError;
    };
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Convenience functions for common error scenarios
export function handleApiError(error: any, context: ErrorContext, customMessage?: string): AppError {
  return errorHandler.handleApiError(error, context, customMessage);
}

export function handleNetworkError(error: Error, context: ErrorContext, customMessage?: string): AppError {
  return errorHandler.handleNetworkError(error, context, customMessage);
}

export function handleValidationError(
  message: string,
  context: ErrorContext,
  fieldErrors?: Record<string, string>
): AppError {
  return errorHandler.handleValidationError(message, context, fieldErrors);
}

export function createRetryFunction(
  originalFunction: () => Promise<any>,
  context: ErrorContext,
  maxRetries?: number,
  delay?: number
): () => Promise<any> {
  return errorHandler.createRetryFunction(originalFunction, context, maxRetries, delay);
}
