/**
 * Performance Optimization Utilities
 * 
 * This module provides utilities for optimizing performance in admin components,
 * including caching, pagination, and loading state management.
 */

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';

// Cache interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

// Cache manager class
export class CacheManager {
  private static instance: CacheManager;
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const expiresAt = now + (ttl || this.defaultTTL);
    
    this.cache.set(key, {
      data,
      timestamp: now,
      expiresAt,
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  invalidate(key: string): void {
    this.cache.delete(key);
  }

  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern);
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  clear(): void {
    this.cache.clear();
  }

  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// Pagination utilities
export interface PaginationConfig {
  page: number;
  limit: number;
  total: number;
}

export interface PaginationResult<T> {
  data: T[];
  pagination: PaginationConfig;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  totalPages: number;
}

export const createPaginationResult = <T>(
  data: T[],
  page: number,
  limit: number,
  total: number
): PaginationResult<T> => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    data,
    pagination: { page, limit, total },
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
    totalPages,
  };
};

// Debounce utility
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Throttle utility
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
};

// Virtual scrolling hook for large lists
export const useVirtualScrolling = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return {
      startIndex,
      endIndex,
      items: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight,
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);

  return {
    ...visibleItems,
    handleScroll,
  };
};

// Optimized data fetching hook with caching
export const useOptimizedFetch = <T>(
  fetchFunction: () => Promise<T>,
  cacheKey: string,
  dependencies: any[] = [],
  options: {
    ttl?: number;
    retryAttempts?: number;
    retryDelay?: number;
    enabled?: boolean;
  } = {}
) => {
  const {
    ttl = 5 * 60 * 1000, // 5 minutes
    retryAttempts = 3,
    retryDelay = 1000,
    enabled = true,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const cache = CacheManager.getInstance();
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchData = useCallback(async (attempt = 1): Promise<void> => {
    if (!enabled) return;

    // Check cache first
    const cachedData = cache.get<T>(cacheKey);
    if (cachedData) {
      setData(cachedData);
      return;
    }

    setLoading(true);
    setError(null);

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    try {
      const result = await fetchFunction();
      
      // Cache the result
      cache.set(cacheKey, result, ttl);
      setData(result);
      setError(null);
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return; // Request was cancelled
      }

      if (attempt < retryAttempts) {
        setTimeout(() => {
          fetchData(attempt + 1);
        }, retryDelay * attempt);
      } else {
        setError(err instanceof Error ? err : new Error('Unknown error'));
      }
    } finally {
      setLoading(false);
    }
  }, [fetchFunction, cacheKey, ttl, retryAttempts, retryDelay, enabled, cache]);

  useEffect(() => {
    fetchData();

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchData, ...dependencies]);

  const refetch = useCallback(() => {
    cache.invalidate(cacheKey);
    fetchData();
  }, [cache, cacheKey, fetchData]);

  const invalidateCache = useCallback(() => {
    cache.invalidate(cacheKey);
  }, [cache, cacheKey]);

  return {
    data,
    loading,
    error,
    refetch,
    invalidateCache,
  };
};

// Intersection Observer hook for lazy loading
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null);
  const elementRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        setEntry(entry);
      },
      options
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [options]);

  return {
    elementRef,
    isIntersecting,
    entry,
  };
};

// Performance monitoring utilities
export const performanceMonitor = {
  startTiming: (label: string): void => {
    performance.mark(`${label}-start`);
  },

  endTiming: (label: string): number => {
    performance.mark(`${label}-end`);
    performance.measure(label, `${label}-start`, `${label}-end`);
    
    const measure = performance.getEntriesByName(label)[0];
    return measure.duration;
  },

  measureComponent: (componentName: string) => {
    return {
      onMount: () => performanceMonitor.startTiming(`${componentName}-mount`),
      onUnmount: () => {
        const duration = performanceMonitor.endTiming(`${componentName}-mount`);
        console.log(`${componentName} mount time: ${duration.toFixed(2)}ms`);
      },
    };
  },

  measureFunction: <T extends (...args: any[]) => any>(
    fn: T,
    label: string
  ): T => {
    return ((...args: any[]) => {
      performanceMonitor.startTiming(label);
      const result = fn(...args);
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const duration = performanceMonitor.endTiming(label);
          console.log(`${label} execution time: ${duration.toFixed(2)}ms`);
        });
      } else {
        const duration = performanceMonitor.endTiming(label);
        console.log(`${label} execution time: ${duration.toFixed(2)}ms`);
        return result;
      }
    }) as T;
  },
};

// Export singleton cache instance
export const cache = CacheManager.getInstance();
