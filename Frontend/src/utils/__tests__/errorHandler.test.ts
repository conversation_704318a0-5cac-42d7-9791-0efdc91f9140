/**
 * Test Suite for Error Handling System
 * 
 * Comprehensive tests for the centralized error handling utilities
 * following <PERSON>'s testing patterns.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  ErrorHandler,
  ErrorType,
  ErrorSeverity,
  handleApiError,
  handleNetworkError,
  handleValidationError,
  createRetryFunction,
} from '../errorHandler';

// Mock toast function
const mockToast = vi.fn();
vi.mock('@/hooks/use-toast', () => ({
  toast: mockToast,
}));

describe('ErrorHandler', () => {
  let errorHandler: ErrorHandler;

  beforeEach(() => {
    errorHandler = ErrorHandler.getInstance();
    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Singleton Pattern', () => {
    it('returns the same instance', () => {
      const instance1 = ErrorHandler.getInstance();
      const instance2 = ErrorHandler.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Error Handling', () => {
    it('handles API errors correctly', () => {
      const apiError = {
        response: {
          status: 400,
          data: {
            message: 'Invalid request data',
            userMessage: 'Please check your input',
          },
        },
      };

      const context = {
        component: 'TestComponent',
        action: 'testAction',
      };

      const result = handleApiError(apiError, context);

      expect(result.type).toBe(ErrorType.VALIDATION_ERROR);
      expect(result.severity).toBe(ErrorSeverity.MEDIUM);
      expect(result.message).toBe('Invalid request data');
      expect(result.userMessage).toBe('Please check your input');
      expect(result.context).toEqual(context);
    });

    it('handles network errors correctly', () => {
      const networkError = new Error('Network connection failed');
      const context = {
        component: 'TestComponent',
        action: 'testAction',
      };

      const result = handleNetworkError(networkError, context);

      expect(result.type).toBe(ErrorType.NETWORK_ERROR);
      expect(result.severity).toBe(ErrorSeverity.HIGH);
      expect(result.message).toBe('Network connection failed');
      expect(result.retryable).toBe(true);
      expect(result.context).toEqual(context);
    });

    it('handles validation errors correctly', () => {
      const message = 'Email is required';
      const context = {
        component: 'TestComponent',
        action: 'validateForm',
      };
      const fieldErrors = { email: 'Email is required' };

      const result = handleValidationError(message, context, fieldErrors);

      expect(result.type).toBe(ErrorType.VALIDATION_ERROR);
      expect(result.severity).toBe(ErrorSeverity.MEDIUM);
      expect(result.message).toBe(message);
      expect(result.userMessage).toBe(message);
      expect(result.retryable).toBe(false);
      expect(result.context?.additionalData).toEqual({ fieldErrors });
    });
  });

  describe('HTTP Status Code Mapping', () => {
    const testCases = [
      {
        status: 401,
        expectedType: ErrorType.AUTHENTICATION_ERROR,
        expectedSeverity: ErrorSeverity.HIGH,
        expectedMessage: 'Authentication required',
      },
      {
        status: 403,
        expectedType: ErrorType.AUTHORIZATION_ERROR,
        expectedSeverity: ErrorSeverity.HIGH,
        expectedMessage: 'Access denied',
      },
      {
        status: 404,
        expectedType: ErrorType.NOT_FOUND_ERROR,
        expectedSeverity: ErrorSeverity.LOW,
        expectedMessage: 'Resource not found',
      },
      {
        status: 408,
        expectedType: ErrorType.TIMEOUT_ERROR,
        expectedSeverity: ErrorSeverity.MEDIUM,
        expectedMessage: 'Request timeout',
      },
      {
        status: 500,
        expectedType: ErrorType.SERVER_ERROR,
        expectedSeverity: ErrorSeverity.HIGH,
        expectedMessage: 'Server error',
      },
    ];

    testCases.forEach(({ status, expectedType, expectedSeverity, expectedMessage }) => {
      it(`maps HTTP ${status} correctly`, () => {
        const apiError = {
          response: {
            status,
            data: {},
          },
        };

        const result = handleApiError(apiError, { component: 'Test' });

        expect(result.type).toBe(expectedType);
        expect(result.severity).toBe(expectedSeverity);
        expect(result.message).toBe(expectedMessage);
      });
    });
  });

  describe('Toast Notifications', () => {
    it('shows toast for high severity errors', () => {
      const error = {
        type: ErrorType.SERVER_ERROR,
        severity: ErrorSeverity.HIGH,
        message: 'Server error occurred',
        userMessage: 'Please try again later',
      };

      errorHandler.handleError(error);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Server Error',
        description: 'Please try again later',
        variant: 'destructive',
        duration: 5000,
      });
    });

    it('shows toast for critical errors with longer duration', () => {
      const error = {
        type: ErrorType.UNKNOWN_ERROR,
        severity: ErrorSeverity.CRITICAL,
        message: 'Critical system error',
        userMessage: 'System is unavailable',
      };

      errorHandler.handleError(error);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Unexpected Error',
        description: 'System is unavailable',
        variant: 'destructive',
        duration: 10000,
      });
    });

    it('shows default variant for low severity errors', () => {
      const error = {
        type: ErrorType.VALIDATION_ERROR,
        severity: ErrorSeverity.LOW,
        message: 'Validation failed',
        userMessage: 'Please check your input',
      };

      errorHandler.handleError(error);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Validation Error',
        description: 'Please check your input',
        variant: 'default',
        duration: 5000,
      });
    });
  });

  describe('Console Logging', () => {
    it('logs high severity errors to console.error', () => {
      const error = {
        type: ErrorType.SERVER_ERROR,
        severity: ErrorSeverity.HIGH,
        message: 'Server error occurred',
        context: { component: 'TestComponent' },
      };

      errorHandler.handleError(error);

      expect(console.error).toHaveBeenCalledWith('Application Error:', expect.objectContaining({
        type: ErrorType.SERVER_ERROR,
        severity: ErrorSeverity.HIGH,
        message: 'Server error occurred',
        context: { component: 'TestComponent' },
      }));
    });

    it('logs medium severity errors to console.warn', () => {
      const error = {
        type: ErrorType.VALIDATION_ERROR,
        severity: ErrorSeverity.MEDIUM,
        message: 'Validation failed',
      };

      errorHandler.handleError(error);

      expect(console.warn).toHaveBeenCalledWith('Application Error:', expect.objectContaining({
        type: ErrorType.VALIDATION_ERROR,
        severity: ErrorSeverity.MEDIUM,
        message: 'Validation failed',
      }));
    });
  });

  describe('Retry Function', () => {
    it('creates a retry function that succeeds on first attempt', async () => {
      const mockFunction = vi.fn().mockResolvedValue('success');
      const context = { component: 'Test', action: 'testRetry' };

      const retryFunction = createRetryFunction(mockFunction, context, 3, 100);
      const result = await retryFunction();

      expect(result).toBe('success');
      expect(mockFunction).toHaveBeenCalledTimes(1);
    });

    it('retries on failure and eventually succeeds', async () => {
      const mockFunction = vi.fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValue('success');

      const context = { component: 'Test', action: 'testRetry' };

      const retryFunction = createRetryFunction(mockFunction, context, 3, 10);
      const result = await retryFunction();

      expect(result).toBe('success');
      expect(mockFunction).toHaveBeenCalledTimes(3);
    });

    it('fails after max retries', async () => {
      const mockFunction = vi.fn().mockRejectedValue(new Error('Persistent failure'));
      const context = { component: 'Test', action: 'testRetry' };

      const retryFunction = createRetryFunction(mockFunction, context, 2, 10);

      await expect(retryFunction()).rejects.toThrow();
      expect(mockFunction).toHaveBeenCalledTimes(2);
    });

    it('does not retry non-retryable errors', async () => {
      const validationError = {
        response: { status: 400, data: { message: 'Bad request' } },
      };
      const mockFunction = vi.fn().mockRejectedValue(validationError);
      const context = { component: 'Test', action: 'testRetry' };

      const retryFunction = createRetryFunction(mockFunction, context, 3, 10);

      await expect(retryFunction()).rejects.toThrow();
      expect(mockFunction).toHaveBeenCalledTimes(1); // No retries for validation errors
    });
  });

  describe('Error Context', () => {
    it('adds timestamp to error context', () => {
      const error = {
        type: ErrorType.API_ERROR,
        severity: ErrorSeverity.MEDIUM,
        message: 'API error',
        context: { component: 'TestComponent' },
      };

      const beforeTime = new Date().toISOString();
      errorHandler.handleError(error);
      const afterTime = new Date().toISOString();

      expect(error.context?.timestamp).toBeDefined();
      expect(error.context?.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
      expect(error.context?.timestamp! >= beforeTime).toBe(true);
      expect(error.context?.timestamp! <= afterTime).toBe(true);
    });

    it('preserves existing timestamp', () => {
      const existingTimestamp = '2024-01-01T00:00:00.000Z';
      const error = {
        type: ErrorType.API_ERROR,
        severity: ErrorSeverity.MEDIUM,
        message: 'API error',
        context: { 
          component: 'TestComponent',
          timestamp: existingTimestamp,
        },
      };

      errorHandler.handleError(error);

      expect(error.context?.timestamp).toBe(existingTimestamp);
    });
  });

  describe('Error Queue and Reporting', () => {
    it('queues errors for reporting', () => {
      const error1 = {
        type: ErrorType.API_ERROR,
        severity: ErrorSeverity.HIGH,
        message: 'First error',
      };

      const error2 = {
        type: ErrorType.NETWORK_ERROR,
        severity: ErrorSeverity.HIGH,
        message: 'Second error',
      };

      errorHandler.handleError(error1);
      errorHandler.handleError(error2);

      // Errors should be queued for batch reporting
      expect(console.log).toHaveBeenCalledWith('Reporting errors to service:', expect.arrayContaining([
        expect.objectContaining({ message: 'First error' }),
        expect.objectContaining({ message: 'Second error' }),
      ]));
    });
  });

  describe('Edge Cases', () => {
    it('handles errors without response object', () => {
      const error = new Error('Generic error');
      const context = { component: 'Test' };

      const result = handleApiError(error, context);

      expect(result.type).toBe(ErrorType.UNKNOWN_ERROR);
      expect(result.message).toBe('Generic error');
    });

    it('handles errors with request but no response', () => {
      const error = {
        request: {},
        message: 'Network error',
      };
      const context = { component: 'Test' };

      const result = handleApiError(error, context);

      expect(result.type).toBe(ErrorType.NETWORK_ERROR);
      expect(result.message).toBe('Network request failed');
    });

    it('handles empty error objects', () => {
      const error = {};
      const context = { component: 'Test' };

      const result = handleApiError(error, context);

      expect(result.type).toBe(ErrorType.UNKNOWN_ERROR);
      expect(result.message).toBe('Unknown error occurred');
    });
  });
});
